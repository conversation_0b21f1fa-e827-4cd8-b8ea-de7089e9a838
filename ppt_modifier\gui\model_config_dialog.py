"""
模型配置对话框模块
提供编辑AI模型配置的图形界面
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QPushButton, QTableWidget,
                            QTableWidgetItem, QGroupBox, QDialogButtonBox,
                            QHeaderView, QTabWidget, QWidget, QMessageBox,
                            QDoubleSpinBox, QCheckBox)
from PyQt5.QtCore import Qt

class ModelConfigDialog(QDialog):
    """模型配置对话框"""
    
    def __init__(self, config_file, parent=None):
        super().__init__(parent)
        self.config_file = config_file
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("模型配置")
        self.setMinimumWidth(600)
        
        # 设置样式表，使表格头部和滚动条区域的白色背景变成黑色
        self.setStyleSheet("""
            QTableWidget {
                background-color: #2B2B2B;
                color: #FFFFFF;
                gridline-color: #3C3F41;
                border: 1px solid #3C3F41;
            }
            QTableWidget::item {
                background-color: #2B2B2B;
                color: #FFFFFF;
            }
            QHeaderView::section {
                background-color: #2B2B2B;
                color: #FFFFFF;
                border: 1px solid #3C3F41;
                padding: 4px;
            }
            QHeaderView::section:vertical {
                background-color: #2B2B2B;
                color: #FFFFFF;
            }
            QHeaderView::section:horizontal {
                background-color: #2B2B2B;
                color: #FFFFFF;
            }
            QTableCornerButton::section {
                background-color: #2B2B2B;
                border: 1px solid #3C3F41;
            }
            QScrollBar {
                background-color: #2B2B2B;
            }
            QScrollBar::handle {
                background-color: #3C3F41;
            }
            QScrollBar::add-line, QScrollBar::sub-line {
                background-color: #2B2B2B;
            }
            QScrollBar::add-page, QScrollBar::sub-page {
                background-color: #2B2B2B;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 为每个提供商创建选项卡
        providers = self.config_file.get_providers()
        for provider_id, provider in providers.items():
            tab = self.create_provider_tab(provider_id, provider)
            self.tab_widget.addTab(tab, provider.get("name", provider_id))
        
        layout.addWidget(self.tab_widget)
        
        # 添加提供商操作的按钮布局
        provider_btn_layout = QHBoxLayout()
        
        # 添加"添加提供商"按钮
        add_provider_btn = QPushButton("添加API提供商")
        add_provider_btn.clicked.connect(self.add_provider)
        provider_btn_layout.addWidget(add_provider_btn)
        
        # 添加"删除提供商"按钮
        delete_provider_btn = QPushButton("删除当前提供商")
        delete_provider_btn.clicked.connect(self.delete_provider)
        provider_btn_layout.addWidget(delete_provider_btn)
        
        layout.addLayout(provider_btn_layout)
        
        # 底部按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def create_provider_tab(self, provider_id, provider):
        """创建提供商选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 基本设置
        form_layout = QFormLayout()
        
        # 提供商名称
        name_edit = QLineEdit(provider.get("name", ""))
        form_layout.addRow("提供商名称:", name_edit)
        
        # API基础URL
        base_url_edit = QLineEdit(provider.get("base_url", ""))
        form_layout.addRow("API基础URL:", base_url_edit)
        
        # API Key
        api_key_edit = QLineEdit(provider.get("api_key", ""))
        api_key_edit.setEchoMode(QLineEdit.Password)  # 密码模式，不直接显示内容
        api_key_show = QPushButton("显示")
        api_key_show.setCheckable(True)
        api_key_show.setChecked(False)
        api_key_show.clicked.connect(lambda checked: api_key_edit.setEchoMode(
            QLineEdit.Normal if checked else QLineEdit.Password
        ))
        
        key_layout = QHBoxLayout()
        key_layout.addWidget(api_key_edit)
        key_layout.addWidget(api_key_show)
        
        form_layout.addRow("API Key:", key_layout)
        
        layout.addLayout(form_layout)
        
        # 模型列表
        model_group = QGroupBox("模型列表")
        model_layout = QVBoxLayout(model_group)
        
        # 创建模型表格
        model_table = QTableWidget()
        model_table.setColumnCount(3)
        model_table.setHorizontalHeaderLabels(["模型ID", "显示名称", "温度"])
        model_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # 设置垂直表头(行号)样式
        model_table.verticalHeader().setStyleSheet("QHeaderView::section { background-color: #2B2B2B; }")
        
        # 设置表格角落部分的背景色
        model_table.setStyleSheet("QTableCornerButton::section { background-color: #2B2B2B; }")
        
        # 填充模型列表
        models = provider.get("models", [])
        model_table.setRowCount(len(models))
        for i, model in enumerate(models):
            model_table.setItem(i, 0, QTableWidgetItem(model.get("id", "")))
            model_table.setItem(i, 1, QTableWidgetItem(model.get("name", "")))
            model_table.setItem(i, 2, QTableWidgetItem(str(model.get("temperature", 0.3))))
        
        model_layout.addWidget(model_table)
        
        # 模型操作按钮
        btn_layout = QHBoxLayout()
        add_btn = QPushButton("添加模型")
        delete_btn = QPushButton("删除模型")
        
        btn_layout.addWidget(add_btn)
        btn_layout.addWidget(delete_btn)
        model_layout.addLayout(btn_layout)
        
        # 添加模型事件
        add_btn.clicked.connect(lambda: self.add_model(provider_id, model_table))
        # 删除模型事件
        delete_btn.clicked.connect(lambda: self.delete_model(provider_id, model_table))
        
        layout.addWidget(model_group)
        
        # 保存对象引用以便后续使用
        tab.name_edit = name_edit
        tab.base_url_edit = base_url_edit
        tab.api_key_edit = api_key_edit
        tab.model_table = model_table
        tab.provider_id = provider_id
        
        return tab
    
    def add_model(self, provider_id, model_table):
        """添加新模型"""
        dialog = QDialog(self)
        dialog.setWindowTitle("添加模型")
        
        layout = QVBoxLayout(dialog)
        
        form = QFormLayout()
        model_id_edit = QLineEdit()
        model_name_edit = QLineEdit()
        temp_spin = QDoubleSpinBox()
        temp_spin.setRange(0.0, 1.0)
        temp_spin.setSingleStep(0.1)
        temp_spin.setValue(0.3)
        
        form.addRow("模型ID:", model_id_edit)
        form.addRow("显示名称:", model_name_edit)
        form.addRow("温度:", temp_spin)
        
        layout.addLayout(form)
        
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            model_id = model_id_edit.text().strip()
            model_name = model_name_edit.text().strip()
            temperature = temp_spin.value()
            
            if model_id and model_name:
                # 添加到表格
                row = model_table.rowCount()
                model_table.insertRow(row)
                model_table.setItem(row, 0, QTableWidgetItem(model_id))
                model_table.setItem(row, 1, QTableWidgetItem(model_name))
                model_table.setItem(row, 2, QTableWidgetItem(str(temperature)))
    
    def delete_model(self, provider_id, model_table):
        """删除选中的模型"""
        selected_rows = set(index.row() for index in model_table.selectedIndexes())
        
        if not selected_rows:
            QMessageBox.warning(self, "警告", "请先选择要删除的模型")
            return
        
        if QMessageBox.question(self, "确认删除", 
                               f"确定要删除选定的{len(selected_rows)}个模型吗？", 
                               QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes:
            # 从后向前删除，避免索引变化
            for row in sorted(selected_rows, reverse=True):
                model_table.removeRow(row)
    
    def add_provider(self):
        """添加新的API提供商"""
        dialog = QDialog(self)
        dialog.setWindowTitle("添加API提供商")
        dialog.setMinimumWidth(400)
        
        layout = QVBoxLayout(dialog)
        
        form = QFormLayout()
        
        # 提供商ID输入框
        provider_id_edit = QLineEdit()
        form.addRow("提供商ID:", provider_id_edit)
        
        # 提供商名称输入框
        provider_name_edit = QLineEdit()
        form.addRow("提供商名称:", provider_name_edit)
        
        # API基础URL输入框
        base_url_edit = QLineEdit()
        base_url_edit.setText("https://api.example.com/v1")
        form.addRow("API基础URL:", base_url_edit)
        
        # API Key输入框
        api_key_edit = QLineEdit()
        api_key_edit.setEchoMode(QLineEdit.Password)  # 密码模式，不直接显示内容
        api_key_show = QPushButton("显示")
        api_key_show.setCheckable(True)
        api_key_show.setChecked(False)
        api_key_show.clicked.connect(lambda checked: api_key_edit.setEchoMode(
            QLineEdit.Normal if checked else QLineEdit.Password
        ))
        
        key_layout = QHBoxLayout()
        key_layout.addWidget(api_key_edit)
        key_layout.addWidget(api_key_show)
        
        form_layout.addRow("API Key:", key_layout)
        
        # 添加默认模型
        add_default_model_checkbox = QCheckBox("添加示例模型")
        add_default_model_checkbox.setChecked(True)
        form.addRow("", add_default_model_checkbox)
        
        layout.addLayout(form)
        
        # 添加说明文本
        info_label = QLabel(
            "注意: 请确保该API提供商兼容OpenAI API格式。\n"
            "添加后，您可以在提供商标签页中添加具体的模型。\n"
            "如勾选添加示例模型，将自动添加OpenAI兼容的通用模型ID示例。"
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 底部按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec_() == QDialog.Accepted:
            provider_id = provider_id_edit.text().strip()
            provider_name = provider_name_edit.text().strip()
            base_url = base_url_edit.text().strip()
            api_key = api_key_edit.text().strip()
            add_default_model = add_default_model_checkbox.isChecked()
            
            # 验证输入
            if not provider_id:
                QMessageBox.warning(self, "输入错误", "提供商ID不能为空")
                return
                
            if not provider_name:
                provider_name = provider_id
                
            if not base_url:
                QMessageBox.warning(self, "输入错误", "API基础URL不能为空")
                return
                
            # 检查ID是否已存在
            providers = self.config_file.get_providers()
            if provider_id in providers:
                QMessageBox.warning(self, "输入错误", f"提供商ID '{provider_id}' 已存在")
                return
                
            # 创建新的提供商配置
            provider_config = {
                "name": provider_name,
                "base_url": base_url,
                "api_key": api_key,
                "models": []
            }
            
            # 添加默认模型
            if add_default_model:
                provider_config["models"] = [
                    {
                        "id": "gpt-3.5-turbo",
                        "name": "GPT-3.5 Turbo",
                        "temperature": 0.3
                    },
                    {
                        "id": "gpt-3.5-turbo-16k",
                        "name": "GPT-3.5 Turbo (16K)",
                        "temperature": 0.3
                    },
                    {
                        "id": "gpt-4",
                        "name": "GPT-4",
                        "temperature": 0.3
                    },
                    {
                        "id": "gpt-4-turbo",
                        "name": "GPT-4 Turbo",
                        "temperature": 0.3
                    }
                ]
            
            # 添加到配置
            providers[provider_id] = provider_config
            
            # 创建新的提供商标签页
            tab = self.create_provider_tab(provider_id, provider_config)
            self.tab_widget.addTab(tab, provider_name)
            
            # 切换到新添加的标签页
            self.tab_widget.setCurrentWidget(tab)
            
            QMessageBox.information(
                self, 
                "添加成功", 
                f"提供商 '{provider_name}' 已添加。\n{'已添加默认示例模型。' if add_default_model else '请在标签页中添加模型。'}"
            )
    
    def delete_provider(self):
        """删除当前选中的提供商"""
        # 获取当前选中的标签页
        current_index = self.tab_widget.currentIndex()
        if current_index < 0:
            return
            
        current_tab = self.tab_widget.widget(current_index)
        current_provider_id = current_tab.provider_id
        current_provider_name = self.tab_widget.tabText(current_index)
        
        # 确认删除
        if QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要删除提供商 '{current_provider_name}' 吗？\n这将删除该提供商的所有模型配置。", 
            QMessageBox.Yes | QMessageBox.No
        ) != QMessageBox.Yes:
            return
            
        # 检查是否至少保留一个提供商
        if self.tab_widget.count() <= 1:
            QMessageBox.warning(self, "无法删除", "必须至少保留一个API提供商。")
            return
            
        # 从配置中删除
        providers = self.config_file.get_providers()
        if current_provider_id in providers:
            del providers[current_provider_id]
            
        # 从UI中删除
        self.tab_widget.removeTab(current_index)
    
    def accept(self):
        """保存配置并关闭对话框"""
        # 遍历所有选项卡，更新配置
        for i in range(self.layout().count()):
            widget = self.layout().itemAt(i).widget()
            if isinstance(widget, QTabWidget):
                for j in range(widget.count()):
                    tab = widget.widget(j)
                    if hasattr(tab, 'provider_id'):
                        self.update_provider_config(tab)
        
        # 保存配置文件
        if self.config_file.save_config():
            super().accept()
        else:
            QMessageBox.warning(self, "错误", "保存配置失败，请检查文件权限")
    
    def update_provider_config(self, tab):
        """更新提供商配置"""
        provider_id = tab.provider_id
        provider = self.config_file.get_provider(provider_id)
        
        if provider:
            # 更新基本设置
            provider["name"] = tab.name_edit.text()
            provider["base_url"] = tab.base_url_edit.text()
            provider["api_key"] = tab.api_key_edit.text()
            
            # 更新模型列表
            models = []
            for row in range(tab.model_table.rowCount()):
                model_id = tab.model_table.item(row, 0).text()
                model_name = tab.model_table.item(row, 1).text()
                try:
                    temperature = float(tab.model_table.item(row, 2).text())
                except:
                    temperature = 0.3
                
                models.append({
                    "id": model_id,
                    "name": model_name,
                    "temperature": temperature
                })
            
            provider["models"] = models 