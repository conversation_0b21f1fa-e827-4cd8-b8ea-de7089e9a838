"""
PPT文件格式转换模块
提供PPT到PPTX格式的安全转换
"""

import os
import time
import shutil
import tempfile
import logging
from typing import Optional
import win32com.client


class PowerPointContext:
    """
    PowerPoint应用程序上下文管理器，确保资源正确释放
    """
    
    def __init__(self, visible: bool = False):
        """
        初始化PowerPoint上下文
        
        Args:
            visible: 是否显示PowerPoint窗口
        """
        self.powerpoint = None
        self.visible = visible
    
    def __enter__(self):
        """
        进入上下文，启动PowerPoint
        
        Returns:
            PowerPoint应用程序对象
        """
        # 先尝试结束所有已有的PowerPoint进程
        try:
            os.system('taskkill /F /IM POWERPNT.EXE 2>nul')
            time.sleep(1)  # 等待进程完全结束
        except Exception as e:
            logging.warning(f"无法终止已有PowerPoint进程: {str(e)}")
        
        try:
            # 启动PowerPoint
            self.powerpoint = win32com.client.DispatchEx("Powerpoint.Application")
            self.powerpoint.DisplayAlerts = 0  # 禁用提示对话框
            self.powerpoint.Visible = self.visible
            return self.powerpoint
        except Exception as e:
            logging.error(f"启动PowerPoint失败: {str(e)}")
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        退出上下文，关闭PowerPoint并清理资源
        """
        if self.powerpoint:
            try:
                self.powerpoint.Quit()
            except Exception as e:
                logging.warning(f"关闭PowerPoint时出错: {str(e)}")
            
            # 强制结束可能残留的PowerPoint进程
            try:
                os.system('taskkill /F /IM POWERPNT.EXE 2>nul')
            except Exception:
                pass
            
            # 释放COM对象
            self.powerpoint = None


def convert_ppt_to_pptx(ppt_path: str) -> Optional[str]:
    """
    将PPT文件转换为PPTX格式
    
    Args:
        ppt_path: PPT文件路径
        
    Returns:
        转换后的PPTX文件路径，失败则返回None
    """
    logging.info(f"开始转换PPT文件: {ppt_path}")
    
    # 确保使用绝对路径
    ppt_path = os.path.abspath(ppt_path)
    
    # 如果文件已经是pptx格式，直接返回
    if ppt_path.lower().endswith('.pptx'):
        logging.info(f"文件已经是PPTX格式: {ppt_path}")
        return ppt_path
    
    # 创建临时目录用于转换
    temp_dir = tempfile.mkdtemp()
    temp_filename = f"temp_{int(time.time())}.pptx"
    temp_pptx_path = os.path.join(temp_dir, temp_filename)
    
    presentation = None
    
    try:
        # 使用上下文管理器处理PowerPoint应用程序
        with PowerPointContext() as powerpoint:
            # 打开PPT文件
            presentation = powerpoint.Presentations.Open(ppt_path, WithWindow=False)
            
            # 保存为PPTX格式到临时文件
            presentation.SaveAs(temp_pptx_path, 24)  # 24表示PPT_SAVEAS_PPTX格式
            presentation.Close()
        
        # 构造最终的pptx文件路径
        final_pptx_path = os.path.splitext(ppt_path)[0] + '.pptx'
        
        # 如果目标文件已存在，先删除
        if os.path.exists(final_pptx_path):
            os.remove(final_pptx_path)
        
        # 将临时文件复制到最终位置
        shutil.copy2(temp_pptx_path, final_pptx_path)
        logging.info(f"PPT文件成功转换为PPTX: {final_pptx_path}")
        
        return final_pptx_path
    
    except Exception as e:
        logging.error(f"转换PPT文件失败: {str(e)}")
        return None
    
    finally:
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except Exception as cleanup_error:
            logging.error(f"清理临时文件时发生错误: {str(cleanup_error)}") 