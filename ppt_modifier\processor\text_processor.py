"""
文本处理模块
提供文本润色和质量检查功能，支持异步调用
"""

import logging
import re
import asyncio
import httpx
from typing import Optional, List, Dict, Any, Tuple
from openai import OpenAI, AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from ..api.api_manager import APIKeyManager
from ..api.model_config import ModelConfigManager


class TextProcessor:
    """
    文本处理类，负责文本润色和质量检查
    """
    
    def __init__(self, api_key_manager: APIKeyManager, prompt: str, model_config_manager: ModelConfigManager, min_text_length: int = 15):
        """
        初始化文本处理器
        
        Args:
            api_key_manager: API密钥管理器
            prompt: 文本润色的提示词
            model_config_manager: 模型配置管理器
            min_text_length: 要处理的最小文本长度
        """
        self.api_key_manager = api_key_manager
        self.prompt = prompt
        self.model_config_manager = model_config_manager
        self.min_text_length = min_text_length
        # 优化超时设置 - 减少超时时间提高响应速度
        self.timeout = httpx.Timeout(
            connect=5.0,   # 从10秒减少到5秒
            read=20.0,     # 从30秒减少到20秒
            write=5.0,     # 从10秒减少到5秒
            pool=5.0       # 从10秒减少到5秒
        )
    
    def _create_client(self, api_key: str) -> OpenAI:
        """
        创建OpenAI客户端
        
        Args:
            api_key: API密钥
            
        Returns:
            OpenAI客户端
        """
        # 获取当前提供商配置
        provider_config = self.model_config_manager.get_current_provider_config()
        if not provider_config:
            logging.error("无法获取当前模型提供商配置")
            # 回退到默认配置
            return OpenAI(
                api_key=api_key,
                base_url="https://api.lingyiwanwu.com/v1",
                timeout=self.timeout
            )
        
        # 确保base_url是有效的ASCII值    
        base_url = provider_config.get("base_url", "https://api.lingyiwanwu.com/v1")
        
        try:
            return OpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=self.timeout
            )
        except Exception as e:
            logging.error(f"创建OpenAI客户端失败: {str(e)}")
            # 回退到默认配置
            return OpenAI(
                api_key=api_key,
                base_url="https://api.lingyiwanwu.com/v1",
                timeout=self.timeout
            )
    
    def _create_async_client(self, api_key: str) -> AsyncOpenAI:
        """
        创建异步OpenAI客户端
        
        Args:
            api_key: API密钥
            
        Returns:
            AsyncOpenAI客户端
        """
        # 获取当前提供商配置
        provider_config = self.model_config_manager.get_current_provider_config()
        if not provider_config:
            logging.error("无法获取当前模型提供商配置")
            # 回退到默认配置
            return AsyncOpenAI(
                api_key=api_key,
                base_url="https://api.lingyiwanwu.com/v1",
                timeout=self.timeout
            )
        
        # 确保base_url是有效的ASCII值    
        base_url = provider_config.get("base_url", "https://api.lingyiwanwu.com/v1")
        
        try:
            return AsyncOpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=self.timeout
            )
        except Exception as e:
            logging.error(f"创建AsyncOpenAI客户端失败: {str(e)}")
            # 回退到默认配置
            return AsyncOpenAI(
                api_key=api_key,
                base_url="https://api.lingyiwanwu.com/v1",
                timeout=self.timeout
            )
    
    @retry(
        retry=retry_if_exception_type((httpx.ConnectTimeout, httpx.ReadTimeout)),
        stop=stop_after_attempt(3),  # 从5次减少到3次
        wait=wait_exponential(multiplier=1, min=1, max=10)  # 减少等待时间
    )
    def polish_text(self, text: str) -> Optional[str]:
        """
        润色文本，使用同步API调用
        
        Args:
            text: 原始文本
            
        Returns:
            润色后的文本，如果处理失败则返回None
        """
        if not text.strip():
            return text
        
        # 检查文本长度是否足够
        if len(text) < self.min_text_length:
            return text
        
        # 检查是否为古诗文，如果是则不进行润色
        try:
            if self.is_classical_chinese(text):
                logging.info(f"检测到古诗文，跳过润色: {text[:30]}...")
                return text
        except Exception as e:
            logging.warning(f"古诗文检查失败，继续处理: {str(e)}")
        
        # 检查缓存
        cached_result = self.api_key_manager.get_cached_text(text)
        if cached_result is not None:
            return cached_result
        
        # 获取API密钥
        api_key = self.model_config_manager.get_api_key(self.api_key_manager)
        if not api_key:
            logging.warning("没有可用的API密钥，跳过文本润色")
            return text
            
        # 获取当前模型配置
        model_config = self.model_config_manager.get_current_model_config()
        if not model_config:
            logging.error("未找到有效的模型配置")
            return text
            
        client = self._create_client(api_key)
        
        try:
            # 构建API调用参数
            model_id = model_config.get("id", "yi-lightning")
            api_params = {
                "model": model_id,
                "messages": [{"role": "user", "content": f"{self.prompt}:\n{text}"}],
                "temperature": float(model_config.get("temperature", 0.3))
            }
            
            # 记录API调用信息（但不记录敏感的API密钥）
            logging.info(f"调用API润色文本，模型ID: {model_id}, 文本长度: {len(text)}")
            
            # 调用API
            response = client.chat.completions.create(**api_params)
            
            if response.choices and response.choices[0].message.content:
                polished_text = response.choices[0].message.content
                
                # 基础验证
                if "润色" in polished_text or "*" in polished_text:
                    return text
                
                if len(polished_text) > len(text) * 2:
                    return text
                
                # 简化质量检查逻辑 - 跳过一些不必要的质量检查以提高速度
                if len(text) <= 100 or (text.strip() != polished_text.strip() and 
                                        self.is_similar_enough(text, polished_text)):
                    # 对于短文本或明显不同但相似度足够的文本不做质量检查
                    # 将结果存入缓存
                    self.api_key_manager.cache_text(text, polished_text)
                    return polished_text
                    
                # 对于长文本或复杂文本做质量检查
                if not self.check_text_quality(client, polished_text, text):
                    logging.info(f"文本质量检查未通过，恢复原文: {text[:30]}...")
                    return text
                
                # 将结果存入缓存
                self.api_key_manager.cache_text(text, polished_text)
                return polished_text
            else:
                return text
                
        except Exception as e:
            logging.error(f"润色文本时发生错误: {str(e)}")
            # 失败后重新获取密钥
            api_key = self.model_config_manager.get_api_key(self.api_key_manager)
            if api_key:
                client.api_key = api_key
            raise  # 重新抛出异常以触发重试
        
        return text
    
    def is_similar_enough(self, text1: str, text2: str) -> bool:
        """
        判断两段文本是否足够相似 - 用于快速验证润色结果
        
        Args:
            text1: 第一段文本
            text2: 第二段文本
            
        Returns:
            如果足够相似返回True，否则返回False
        """
        # 提取数字和标点符号
        import re
        pattern = r'[0-9.,，。!?！？;；:：]'
        
        # 检查数字和标点符号是否基本保留
        symbols1 = re.findall(pattern, text1)
        symbols2 = re.findall(pattern, text2)
        
        # 如果两个文本的长度差别不大，且基本标点和数字保留，认为是足够相似
        if 0.7 <= len(text2) / len(text1) <= 1.5 and len(symbols1) > 0:
            symbol_ratio = len(symbols2) / len(symbols1)
            return 0.7 <= symbol_ratio <= 1.3
            
        return False
    
    async def polish_text_async(self, text: str) -> Optional[str]:
        """
        润色文本，使用异步API调用
        
        Args:
            text: 原始文本
            
        Returns:
            润色后的文本，如果处理失败则返回None
        """
        if not text.strip():
            return text
        
        # 检查文本长度是否足够
        if len(text) < self.min_text_length:
            return text
        
        # 检查缓存
        cached_result = self.api_key_manager.get_cached_text(text)
        if cached_result is not None:
            return cached_result
        
        # 获取API密钥
        api_key = self.model_config_manager.get_api_key(self.api_key_manager)
        if not api_key:
            logging.warning("没有可用的API密钥，跳过文本润色")
            return text
            
        # 获取当前模型配置
        model_config = self.model_config_manager.get_current_model_config()
        if not model_config:
            logging.error("未找到有效的模型配置")
            return text
        
        async_client = self._create_async_client(api_key)
        
        try:
            # 构建API调用参数
            model_id = model_config.get("id", "yi-lightning")
            api_params = {
                "model": model_id,
                "messages": [{"role": "user", "content": f"{self.prompt}:\n{text}"}],
                "temperature": float(model_config.get("temperature", 0.3))
            }
            
            # 记录API调用信息（但不记录敏感的API密钥）
            logging.info(f"异步调用API润色文本，模型ID: {model_id}, 文本长度: {len(text)}")
            
            # 调用API
            response = await async_client.chat.completions.create(**api_params)
            
            if response.choices and response.choices[0].message.content:
                polished_text = response.choices[0].message.content
                
                # 基础验证
                if "润色" in polished_text or "*" in polished_text:
                    return text
                
                if len(polished_text) > len(text) * 2:
                    return text
                
                # 异步方式检查质量
                client = self._create_client(api_key)  # 同步客户端用于质量检查
                if not self.check_text_quality(client, polished_text, text):
                    logging.info(f"文本质量检查未通过，恢复原文: {text[:30]}...")
                    return text
                
                # 将结果存入缓存
                self.api_key_manager.cache_text(text, polished_text)
                return polished_text
            else:
                return text
                
        except Exception as e:
            logging.error(f"异步润色文本时发生错误: {str(e)}")
            return text
    
    def check_text_quality(self, client: OpenAI, polished_text: str, original_text: str) -> bool:
        """
        检查润色后的文本质量
        
        Args:
            client: OpenAI客户端
            polished_text: 润色后的文本
            original_text: 原始文本
            
        Returns:
            如果质量检查通过返回True，否则返回False
        """
        # 检查缓存
        cache_key = f"quality_{polished_text}"
        cached_result = self.api_key_manager.get_cached_text(cache_key)
        if cached_result is not None:
            return cached_result == "true"
        
        try:
            # 获取当前模型配置
            model_config = self.model_config_manager.get_current_model_config()
            if not model_config:
                logging.error("未找到有效的模型配置")
                # 出错时默认通过
                return True
                
            # 构建更详细的提示词，要求AI比较原文和润色后的文本
            prompt = f"""
请对比以下原始文本和润色后的文本，判断以下三点：
1. 润色后的文本是否与原始文本意思一致
2. 润色后的文本中的序号（如1、2、3或一、二、三等）是否完整保留
3. 润色后的文本中的标点符号是否正确

原始文本：
{original_text}

润色后的文本：
{polished_text}

只需回答"通过"或"不通过"，不需要解释。如果三点全部满足则回答"通过"，任何一点不满足则回答"不通过"。"""

            # 构建API调用参数
            model_id = model_config.get("id", "yi-lightning")
            api_params = {
                "model": model_id,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1
            }
            
            # 记录API调用信息
            logging.info(f"调用API检查文本质量，模型ID: {model_id}")
            
            # 调用API
            response = client.chat.completions.create(**api_params)

            if response.choices and response.choices[0].message.content:
                result = "通过" in response.choices[0].message.content
                # 缓存结果
                self.api_key_manager.cache_text(cache_key, "true" if result else "false")
                logging.info(f"文本质量检查结果: {'通过' if result else '不通过'}")
                return result
        except Exception as e:
            logging.error(f"文本质量检查失败: {str(e)}")
            # 出错时默认通过
            return True

        return True  # 默认通过
    
    async def check_text_quality_async(self, client: AsyncOpenAI, polished_text: str, original_text: str) -> bool:
        """
        异步检查润色后的文本质量
        
        Args:
            client: AsyncOpenAI客户端
            polished_text: 润色后的文本
            original_text: 原始文本
            
        Returns:
            如果质量检查通过返回True，否则返回False
        """
        # 检查缓存
        cache_key = f"quality_{polished_text}"
        cached_result = self.api_key_manager.get_cached_text(cache_key)
        if cached_result is not None:
            return cached_result == "true"
        
        try:
            # 获取当前模型配置
            model_config = self.model_config_manager.get_current_model_config()
            if not model_config:
                logging.error("未找到有效的模型配置")
                # 出错时默认通过
                return True
                
            # 构建更详细的提示词，要求AI比较原文和润色后的文本
            prompt = f"""
请对比以下原始文本和润色后的文本，判断以下三点：
1. 润色后的文本是否与原始文本意思一致
2. 润色后的文本中的序号（如1、2、3或一、二、三等）是否完整保留
3. 润色后的文本中的标点符号是否正确

原始文本：
{original_text}

润色后的文本：
{polished_text}

只需回答"通过"或"不通过"，不需要解释。如果三点全部满足则回答"通过"，任何一点不满足则回答"不通过"。"""

            # 构建API调用参数
            api_params = {
                "model": model_config.get("id", "yi-lightning"),
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1
            }
            
            # 调用API
            response = await client.chat.completions.create(**api_params)

            if response.choices and response.choices[0].message.content:
                result = "通过" in response.choices[0].message.content
                # 缓存结果
                self.api_key_manager.cache_text(cache_key, "true" if result else "false")
                logging.info(f"文本质量检查结果: {'通过' if result else '不通过'}")
                return result
        except Exception as e:
            logging.error(f"异步文本质量检查失败: {str(e)}")
            # 出错时默认通过
            return True

        return True  # 默认通过
    
    @retry(
        retry=retry_if_exception_type((httpx.ConnectTimeout, httpx.ReadTimeout)),
        stop=stop_after_attempt(5),  # 增加到5次重试
        wait=wait_exponential(multiplier=1, min=1, max=10)
    )
    def is_classical_chinese(self, text: str) -> bool:
        """
        判断文本是否为古诗文
        
        Args:
            text: 要判断的文本
            
        Returns:
            是否为古诗文
        """
        # 简化判断方法，避免API调用，只使用规则判断
        
        # 1. 包含明显的古诗词标记，如"《》"、"。，"、"，。"
        if re.search(r'[《》「」『』]', text) and re.search(r'[。，；？！：]', text):
            return True
            
        # 2. 行数少，每行字数少且整齐，多为五言或七言
        lines = [line.strip() for line in text.strip().split('\n') if line.strip()]
        if len(lines) >= 2 and len(lines) <= 20:
            lengths = [len(line) for line in lines]
            # 检查是否为常见的古诗长度模式
            if all(l == 5 or l == 7 for l in lengths) and len(set(lengths)) <= 2:
                return True
        
        # 3. 含有典型古文词语或虚词
        classical_words = ["之乎者也", "兮", "其", "而", "焉", "矣", "乎", "哉", "耶"]
        for word in classical_words:
            if word in text:
                return True
                
        return False
            
    def set_active_model(self, model_id):
        """设置活动模型ID"""
        if hasattr(self.model_config_manager, 'active_model'):
            self.model_config_manager.active_model = model_id 