# 预览功能统一化设计文档

## 概述

本设计文档描述了如何统一PPT文本润色工具中的三个预览功能，使它们都使用相同的底层函数`preview_file()`，以提高代码一致性和维护性。

## 架构设计

### 当前架构分析

```
当前架构：
拖放功能 → dropEvent() → preview_file() [部分情况]
预览修改 → preview_ppt() → PPT处理器 → PreviewWindow
随机预览 → random_preview_unreviewed_file() → preview_file()
```

### 目标架构设计

```
目标架构：
拖放功能 → dropEvent() → preview_file()
预览修改 → preview_ppt() → preview_file()
随机预览 → random_preview_unreviewed_file() → preview_file() [保持不变]
```

## 组件设计

### 1. 核心预览函数 (`preview_file`)

**功能：** 作为所有预览功能的统一入口点

**输入参数：**
- `file_path`: 要预览的PPT文件路径

**处理逻辑：**
1. 验证文件路径有效性
2. 检查是否有正在运行的任务
3. 创建API密钥管理器和文本处理器
4. 清理旧的预览窗口
5. 创建新的预览窗口
6. 加载文件到预览窗口
7. 显示预览窗口

**错误处理：**
- 文件不存在或无效
- 任务冲突处理
- 预览窗口创建失败

### 2. 拖放功能重构 (`dropEvent`)

**当前问题：**
- 对不同类型文件有不同处理逻辑
- 包含用户确认对话框
- 逻辑复杂，维护困难

**重构方案：**
1. 简化文件类型判断逻辑
2. 移除用户确认对话框
3. 统一调用`preview_file()`函数
4. 保持文件路径设置和文件夹检测功能

**新的处理流程：**
```
拖放文件 → 验证文件类型 → 设置文件路径 → 自动检测文件夹 → preview_file()
```

### 3. 预览修改按钮重构 (`preview_ppt`)

**当前问题：**
- 包含复杂的PPT处理逻辑
- 创建临时文件
- 与其他预览功能逻辑不一致

**重构方案：**
1. 移除PPT处理逻辑
2. 简化为文件选择和预览调用
3. 统一使用`preview_file()`函数
4. 保持错误处理和用户提示

**新的处理流程：**
```
点击按钮 → 获取选择的文件 → 验证文件 → preview_file()
```

### 4. 预览窗口创建统一化

**统一参数：**
- `api_key_manager`: API密钥管理器
- `text_processor`: 文本处理器
- 窗口标志: `Qt.Window`
- 样式表: 统一的预览样式

**统一逻辑：**
1. 窗口清理逻辑
2. 窗口创建参数
3. 样式表应用
4. 窗口显示方式

## 数据流设计

### 统一后的数据流

```mermaid
graph TD
    A[用户操作] --> B{操作类型}
    B -->|拖放文件| C[dropEvent]
    B -->|点击预览修改| D[preview_ppt]
    B -->|点击随机预览| E[random_preview_unreviewed_file]
    
    C --> F[设置文件路径]
    C --> G[自动检测文件夹]
    D --> H[获取选择的文件]
    E --> I[搜索并选择未审阅文件]
    
    F --> J[preview_file]
    G --> J
    H --> J
    I --> J
    
    J --> K[创建API管理器]
    J --> L[创建文本处理器]
    J --> M[清理旧窗口]
    J --> N[创建预览窗口]
    J --> O[显示预览窗口]
```

## 接口设计

### 1. 统一预览接口

```python
def preview_file(self, file_path: str) -> None:
    """
    统一的文件预览接口
    
    Args:
        file_path: PPT文件路径
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 文件格式不支持
        RuntimeError: 预览窗口创建失败
    """
```

### 2. 重构后的拖放接口

```python
def dropEvent(self, event) -> None:
    """
    处理文件拖放事件
    
    Args:
        event: 拖放事件对象
    """
```

### 3. 重构后的预览修改接口

```python
def preview_ppt(self) -> None:
    """
    预览修改按钮处理函数
    """
```

## 错误处理设计

### 统一错误处理策略

1. **文件验证错误**
   - 文件不存在
   - 文件格式不支持
   - 文件权限问题

2. **任务冲突错误**
   - 有正在运行的任务
   - 预览窗口已存在

3. **系统资源错误**
   - 内存不足
   - 窗口创建失败

### 错误处理流程

```
错误发生 → 记录日志 → 显示用户友好的错误信息 → 清理资源 → 恢复界面状态
```

## 测试策略

### 单元测试

1. `preview_file()`函数测试
2. 拖放事件处理测试
3. 预览修改按钮测试
4. 错误处理测试

### 集成测试

1. 三个预览功能的一致性测试
2. 预览窗口创建和显示测试
3. 文件路径处理测试
4. 用户界面交互测试

### 用户验收测试

1. 拖放文件预览功能测试
2. 预览修改按钮功能测试
3. 随机预览功能回归测试
4. 用户体验一致性测试

## 性能考虑

### 优化点

1. **窗口复用**：避免频繁创建和销毁预览窗口
2. **资源管理**：及时清理不需要的资源
3. **异步处理**：避免界面阻塞

### 内存管理

1. 预览窗口的生命周期管理
2. 大文件处理时的内存优化
3. 垃圾回收机制

## 向后兼容性

### 保持不变的功能

1. 用户界面布局和外观
2. 操作方式和交互逻辑
3. 文件路径处理方式
4. 错误提示信息

### 改进的功能

1. 代码一致性和可维护性
2. 错误处理的统一性
3. 性能和资源使用效率

## 实施计划

### 阶段1：核心函数优化
- 完善`preview_file()`函数
- 统一预览窗口创建逻辑

### 阶段2：拖放功能重构
- 简化`dropEvent()`处理逻辑
- 移除不必要的用户确认

### 阶段3：预览修改重构
- 重构`preview_ppt()`函数
- 移除PPT处理逻辑

### 阶段4：测试和优化
- 全面测试三个功能
- 性能优化和错误处理完善