# 预览功能统一化需求文档

## 介绍

本需求旨在统一PPT文本润色工具中的三个预览功能，使"拖放已修改的PPT文件"和"预览修改"功能使用与"随机预览一个未审阅文件"相同的底层函数实现，以提高代码一致性和维护性。

## 需求

### 需求1：统一拖放预览功能

**用户故事：** 作为用户，我希望拖放PPT文件到界面时能够直接预览文件，就像随机预览功能一样简单直接。

#### 验收标准

1. WHEN 用户拖放PPT文件到拖放区域 THEN 系统应该使用与随机预览相同的`preview_file()`函数来处理文件
2. WHEN 拖放的是preview开头的文件 THEN 系统应该直接调用`preview_file()`而不进行额外的文件类型判断
3. WHEN 拖放的是其他PPT文件 THEN 系统应该直接调用`preview_file()`而不询问用户是否预览
4. WHEN 拖放功能执行时 THEN 系统应该保持原有的文件路径设置功能
5. WHEN 拖放功能执行时 THEN 系统应该保持原有的文件夹自动检测功能

### 需求2：统一预览修改按钮功能

**用户故事：** 作为用户，我希望点击"预览修改"按钮时能够直接预览当前选择的文件，而不需要重新处理文件。

#### 验收标准

1. WHEN 用户点击"预览修改"按钮 THEN 系统应该使用`preview_file()`函数直接预览当前选择的文件
2. WHEN 没有选择文件时 THEN 系统应该显示警告信息提示用户选择文件
3. WHEN 选择了多个文件时 THEN 系统应该预览第一个文件
4. WHEN 预览修改功能执行时 THEN 系统应该移除原有的PPT处理步骤
5. WHEN 预览修改功能执行时 THEN 系统应该保持原有的API密钥管理器和文本处理器创建逻辑

### 需求3：保持随机预览功能不变

**用户故事：** 作为用户，我希望随机预览功能保持现有的行为，作为其他两个功能的参考标准。

#### 验收标准

1. WHEN 用户点击"随机预览一个未审阅文件"按钮 THEN 系统应该保持现有的搜索和选择逻辑
2. WHEN 随机预览功能执行时 THEN 系统应该继续使用`preview_file()`函数
3. WHEN 随机预览功能执行时 THEN 系统应该保持现有的进度显示和错误处理

### 需求4：统一预览窗口创建逻辑

**用户故事：** 作为用户，我希望所有预览功能都使用相同的预览窗口创建方式，确保一致的用户体验。

#### 验收标准

1. WHEN 任何预览功能被触发时 THEN 系统应该使用相同的`PreviewWindow`初始化参数
2. WHEN 预览窗口创建时 THEN 系统应该使用相同的样式表和窗口标志
3. WHEN 预览窗口显示时 THEN 系统应该使用相同的窗口显示逻辑
4. WHEN 存在旧的预览窗口时 THEN 系统应该使用相同的窗口清理逻辑

### 需求5：保持向后兼容性

**用户故事：** 作为用户，我希望功能统一后，原有的用户界面和操作方式保持不变。

#### 验收标准

1. WHEN 功能统一完成后 THEN 用户界面应该保持原有的外观和布局
2. WHEN 功能统一完成后 THEN 用户的操作方式应该保持不变
3. WHEN 功能统一完成后 THEN 系统应该保持原有的错误处理和日志记录
4. WHEN 功能统一完成后 THEN 系统应该保持原有的文件路径处理逻辑
5. WHEN 功能统一完成后 THEN 系统应该保持原有的进度显示功能