# 预览功能统一化实施计划

## 任务列表

- [x] 1. 优化核心预览函数


  - 完善`preview_file()`函数的错误处理和资源管理
  - 统一预览窗口创建逻辑和参数
  - 添加详细的日志记录和调试信息
  - _需求: 1.1, 2.1, 4.1, 4.2_

- [ ] 2. 重构拖放功能
  - [x] 2.1 简化dropEvent()处理逻辑





    - 移除对preview文件的特殊判断逻辑
    - 移除用户确认对话框
    - 统一调用preview_file()函数
    - _需求: 1.1, 1.3_

  - [x] 2.2 保持文件路径设置功能





    - 确保拖放后正确设置ppt_file_edit文本框
    - 保持自动检测文件夹功能
    - 保持对selective文件的限制检查
    - _需求: 1.4, 1.5_

- [x] 3. 重构预览修改按钮功能












  - [x] 3.1 简化preview_ppt()函数








    - 移除PPT处理器相关代码
    - 移除临时文件生成逻辑
    - 简化为直接调用preview_file()
    - _需求: 2.1, 2.4_

  - [x] 3.2 保持文件选择逻辑


    - 保持get_selected_files()调用
    - 保持文件验证和错误提示
    - 处理多文件选择情况（使用第一个文件）
    - _需求: 2.2, 2.3_

- [x] 4. 统一预览窗口创建





























  - [x] 4.1 标准化PreviewWindow初始化
























    - 统一API密钥管理器创建方式
    - 统一文本处理器创建方式
    - 统一窗口标志和样式设置
    - _需求: 4.1, 4.2_

  - [x] 4.2 统一窗口生命周期管理


    - 标准化旧窗口清理逻辑
    - 统一窗口显示和激活方式
    - 统一窗口标题设置规则
    - _需求: 4.3, 4.4_

- [x] 5. 完善错误处理

















  - [x] 5.1 统一错误处理机制






    - 标准化文件验证错误处理
    - 统一任务冲突检查和提示
    - 标准化系统资源错误处理
    - _需求: 5.3_

  - [x] 5.2 保持向后兼容性


    - 确保用户界面保持不变
    - 保持原有操作方式
    - 保持原有错误提示信息
    - _需求: 5.1, 5.2, 5.4_

- [ ] 6. 测试和验证



  - [x] 6.1 功能测试
    - 测试拖放文件预览功能
    - 测试预览修改按钮功能
    - 测试随机预览功能（回归测试）
    - _需求: 1.1, 2.1, 3.1_

  - [x] 6.2 一致性测试


    - 验证三个功能使用相同的preview_file()函数
    - 验证预览窗口创建的一致性
    - 验证错误处理的一致性
    - _需求: 4.1, 4.2, 4.3_

- [x] 7. 代码优化和清理









  - [x] 7.1 移除冗余代码
    - 清理preview_ppt()中的PPT处理逻辑
    - 移除不再使用的临时文件处理代码
    - 清理dropEvent()中的复杂判断逻辑
    - _需求: 2.4_

  - [x] 7.2 代码重构和优化


    - 提取公共的预览窗口创建逻辑
    - 优化资源管理和内存使用
    - 完善日志记录和调试信息
    - _需求: 5.5_

- [x] 8. 文档更新




  - [x] 8.1 更新代码注释
















    - 更新函数文档字符串
    - 添加重构说明注释
    - 更新错误处理说明
    - _需求: 5.3_

  - [x] 8.2 更新用户文档


    - 确认用户操作方式未改变
    - 更新功能说明（如有必要）
    - 更新故障排除指南
    - _需求: 5.1, 5.2_