#!/usr/bin/env python3
"""
预览功能统一化测试脚本

测试三个预览功能的一致性和正确性：
1. 拖放文件预览功能
2. 预览修改按钮功能  
3. 随机预览功能（回归测试）

需求: 1.1, 2.1, 3.1, 4.1, 4.2, 4.3
"""

import os
import sys
import unittest
import tempfile
import shutil
import logging
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QMimeData, QUrl
from PyQt5.QtGui import QDropEvent

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ppt_modifier.gui.main_window import MainWindow, ErrorHandler
from ppt_modifier.api.api_manager import APIKeyManager
from ppt_modifier.processor.text_processor import TextProcessor


class TestPreviewFunctionUnification(unittest.TestCase):
    """预览功能统一化测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        # 创建QApplication实例
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
        
        # 设置日志
        logging.basicConfig(level=logging.DEBUG)
        cls.logger = logging.getLogger(__name__)
        
        # 创建临时测试目录
        cls.test_dir = tempfile.mkdtemp(prefix="preview_test_")
        cls.logger.info(f"创建测试目录: {cls.test_dir}")
        
        # 创建测试PPT文件
        cls.test_ppt_file = os.path.join(cls.test_dir, "test_presentation.pptx")
        cls.preview_ppt_file = os.path.join(cls.test_dir, "preview_test_20241201_120000.pptx")
        cls.selective_ppt_file = os.path.join(cls.test_dir, "test_selective.pptx")
        
        # 创建空的测试文件
        for file_path in [cls.test_ppt_file, cls.preview_ppt_file, cls.selective_ppt_file]:
            with open(file_path, 'wb') as f:
                f.write(b'PK')  # 简单的文件头，模拟PPT文件
        
        cls.logger.info("测试文件创建完成")
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理测试目录
        if os.path.exists(cls.test_dir):
            shutil.rmtree(cls.test_dir)
            cls.logger.info(f"清理测试目录: {cls.test_dir}")
    
    def setUp(self):
        """每个测试方法的初始化"""
        # 创建主窗口实例
        self.main_window = MainWindow()
        
        # Mock API密钥管理器和文本处理器
        self.mock_api_manager = Mock(spec=APIKeyManager)
        self.mock_text_processor = Mock(spec=TextProcessor)
        
        # Mock预览窗口
        self.mock_preview_window = Mock()
        self.mock_preview_window.isVisible.return_value = False
        self.mock_preview_window.load_ppt = Mock()
        self.mock_preview_window.show = Mock()
        self.mock_preview_window.raise_ = Mock()
        self.mock_preview_window.setWindowTitle = Mock()
        self.mock_preview_window.setWindowFlags = Mock()
        self.mock_preview_window.setStyleSheet = Mock()
        
        # Mock主窗口的方法
        self.main_window.create_api_key_manager = Mock(return_value=self.mock_api_manager)
        self.main_window.create_text_processor = Mock(return_value=self.mock_text_processor)
        self.main_window._create_preview_window = Mock(return_value=self.mock_preview_window)
        self.main_window.get_preview_style = Mock(return_value="mock_style")
        
        # Mock工作线程
        self.main_window.worker_thread = None
        
        self.logger.info("测试环境初始化完成")
    
    def tearDown(self):
        """每个测试方法的清理"""
        if hasattr(self, 'main_window'):
            self.main_window.close()
    
    def test_preview_file_function_exists(self):
        """测试preview_file函数是否存在且可调用"""
        self.assertTrue(hasattr(self.main_window, 'preview_file'))
        self.assertTrue(callable(getattr(self.main_window, 'preview_file')))
        self.logger.info("✓ preview_file函数存在且可调用")
    
    def test_preview_file_basic_functionality(self):
        """测试preview_file函数的基本功能"""
        # 测试正常文件预览
        with patch.object(ErrorHandler, 'validate_ppt_file_for_preview'):
            with patch.object(ErrorHandler, 'check_task_conflict'):
                with patch.object(ErrorHandler, 'validate_system_resources'):
                    self.main_window.preview_file(self.test_ppt_file)
                    
                    # 验证API管理器和文本处理器被创建
                    self.main_window.create_api_key_manager.assert_called_once()
                    self.main_window.create_text_processor.assert_called_once()
                    
                    # 验证预览窗口被创建和显示
                    self.main_window._create_preview_window.assert_called_once_with(
                        self.mock_api_manager, self.mock_text_processor
                    )
                    self.mock_preview_window.load_ppt.assert_called_once_with(self.test_ppt_file)
                    self.mock_preview_window.show.assert_called_once()
                    
        self.logger.info("✓ preview_file基本功能测试通过")
    
    def test_preview_file_error_handling(self):
        """测试preview_file函数的错误处理"""
        # 测试文件不存在的情况
        non_existent_file = os.path.join(self.test_dir, "non_existent.pptx")
        
        with patch.object(QMessageBox, 'warning') as mock_warning:
            self.main_window.preview_file(non_existent_file)
            mock_warning.assert_called()
        
        # 测试selective文件的情况
        with patch.object(QMessageBox, 'warning') as mock_warning:
            self.main_window.preview_file(self.selective_ppt_file)
            mock_warning.assert_called()
        
        self.logger.info("✓ preview_file错误处理测试通过")
    
    def test_drag_drop_functionality(self):
        """测试拖放文件预览功能"""
        # 创建模拟拖放事件
        mock_event = Mock(spec=QDropEvent)
        mock_mime_data = Mock(spec=QMimeData)
        mock_url = Mock(spec=QUrl)
        
        # 设置模拟数据
        mock_url.toLocalFile.return_value = self.test_ppt_file
        mock_mime_data.urls.return_value = [mock_url]
        mock_mime_data.hasUrls.return_value = True
        mock_event.mimeData.return_value = mock_mime_data
        
        # Mock preview_file方法
        with patch.object(self.main_window, 'preview_file') as mock_preview_file:
            with patch.object(self.main_window, 'auto_detect_folders_from_file'):
                self.main_window.dropEvent(mock_event)
                
                # 验证preview_file被调用
                mock_preview_file.assert_called_once_with(self.test_ppt_file)
                
                # 验证文件路径被设置
                self.assertEqual(self.main_window.ppt_file_edit.text(), self.test_ppt_file)
        
        self.logger.info("✓ 拖放文件预览功能测试通过")
    
    def test_drag_drop_selective_file_rejection(self):
        """测试拖放selective文件被正确拒绝"""
        # 创建模拟拖放事件
        mock_event = Mock(spec=QDropEvent)
        mock_mime_data = Mock(spec=QMimeData)
        mock_url = Mock(spec=QUrl)
        
        # 设置模拟数据为selective文件
        mock_url.toLocalFile.return_value = self.selective_ppt_file
        mock_mime_data.urls.return_value = [mock_url]
        mock_mime_data.hasUrls.return_value = True
        mock_event.mimeData.return_value = mock_mime_data
        
        # Mock错误处理
        with patch.object(ErrorHandler, 'handle_error') as mock_handle_error:
            self.main_window.dropEvent(mock_event)
            
            # 验证错误处理被调用
            mock_handle_error.assert_called()
        
        self.logger.info("✓ 拖放selective文件拒绝测试通过")
    
    def test_preview_ppt_button_functionality(self):
        """测试预览修改按钮功能"""
        # 设置选择的文件
        self.main_window.ppt_file_edit.setText(self.test_ppt_file)
        
        # Mock get_selected_files方法
        with patch.object(self.main_window, 'get_selected_files', return_value=[self.test_ppt_file]):
            with patch.object(self.main_window, 'preview_file') as mock_preview_file:
                self.main_window.preview_ppt()
                
                # 验证preview_file被调用
                mock_preview_file.assert_called_once_with(self.test_ppt_file)
        
        self.logger.info("✓ 预览修改按钮功能测试通过")
    
    def test_preview_ppt_no_file_selected(self):
        """测试预览修改按钮在未选择文件时的处理"""
        # Mock get_selected_files返回空列表
        with patch.object(self.main_window, 'get_selected_files', return_value=[]):
            with patch.object(ErrorHandler, 'handle_error') as mock_handle_error:
                self.main_window.preview_ppt()
                
                # 验证错误处理被调用
                mock_handle_error.assert_called()
        
        self.logger.info("✓ 预览修改按钮无文件选择测试通过")
    
    def test_preview_ppt_multiple_files(self):
        """测试预览修改按钮处理多文件选择的情况"""
        files = [self.test_ppt_file, self.preview_ppt_file]
        
        # Mock get_selected_files返回多个文件
        with patch.object(self.main_window, 'get_selected_files', return_value=files):
            with patch.object(self.main_window, 'preview_file') as mock_preview_file:
                self.main_window.preview_ppt()
                
                # 验证只预览第一个文件
                mock_preview_file.assert_called_once_with(self.test_ppt_file)
        
        self.logger.info("✓ 预览修改按钮多文件选择测试通过")
    
    def test_random_preview_functionality(self):
        """测试随机预览功能（回归测试）"""
        # 设置输出文件夹
        self.main_window.output_path_edit.setText(self.test_dir)
        
        # Mock preview_file方法
        with patch.object(self.main_window, 'preview_file') as mock_preview_file:
            with patch('random.choice', return_value=self.preview_ppt_file):
                self.main_window.random_preview_unreviewed_file()
                
                # 验证preview_file被调用
                mock_preview_file.assert_called_once_with(self.preview_ppt_file)
        
        self.logger.info("✓ 随机预览功能测试通过")
    
    def test_preview_window_creation_consistency(self):
        """测试预览窗口创建的一致性"""
        # 测试三个功能都使用相同的预览窗口创建方式
        test_cases = [
            ("preview_file", lambda: self.main_window.preview_file(self.test_ppt_file)),
            ("drag_drop", self._simulate_drag_drop),
            ("preview_button", self._simulate_preview_button)
        ]
        
        for test_name, test_func in test_cases:
            with self.subTest(test_name=test_name):
                # 重置mock
                self.main_window._create_preview_window.reset_mock()
                
                # 执行测试
                with patch.object(ErrorHandler, 'validate_ppt_file_for_preview'):
                    with patch.object(ErrorHandler, 'check_task_conflict'):
                        with patch.object(ErrorHandler, 'validate_system_resources'):
                            test_func()
                            
                            # 验证预览窗口创建参数一致
                            if self.main_window._create_preview_window.called:
                                args, kwargs = self.main_window._create_preview_window.call_args
                                self.assertEqual(len(args), 2)  # api_manager, text_processor
                                self.assertEqual(args[0], self.mock_api_manager)
                                self.assertEqual(args[1], self.mock_text_processor)
        
        self.logger.info("✓ 预览窗口创建一致性测试通过")
    
    def _simulate_drag_drop(self):
        """模拟拖放操作"""
        mock_event = Mock(spec=QDropEvent)
        mock_mime_data = Mock(spec=QMimeData)
        mock_url = Mock(spec=QUrl)
        
        mock_url.toLocalFile.return_value = self.test_ppt_file
        mock_mime_data.urls.return_value = [mock_url]
        mock_mime_data.hasUrls.return_value = True
        mock_event.mimeData.return_value = mock_mime_data
        
        with patch.object(self.main_window, 'auto_detect_folders_from_file'):
            self.main_window.dropEvent(mock_event)
    
    def _simulate_preview_button(self):
        """模拟预览按钮点击"""
        with patch.object(self.main_window, 'get_selected_files', return_value=[self.test_ppt_file]):
            self.main_window.preview_ppt()
    
    def test_error_handling_consistency(self):
        """测试错误处理的一致性"""
        # 测试所有三个功能都使用相同的错误处理机制
        non_existent_file = os.path.join(self.test_dir, "non_existent.pptx")
        
        error_test_cases = [
            ("preview_file", lambda: self.main_window.preview_file(non_existent_file)),
            ("preview_button", self._simulate_preview_button_error),
            ("drag_drop", self._simulate_drag_drop_error)
        ]
        
        for test_name, test_func in error_test_cases:
            with self.subTest(test_name=test_name):
                with patch.object(ErrorHandler, 'handle_error') as mock_handle_error:
                    test_func()
                    
                    # 验证错误处理被调用
                    if mock_handle_error.called:
                        args, kwargs = mock_handle_error.call_args
                        self.assertEqual(args[0], self.main_window)  # parent_widget
                        self.assertIsInstance(args[1], Exception)  # error
                        self.assertIsInstance(args[2], str)  # operation
        
        self.logger.info("✓ 错误处理一致性测试通过")
    
    def _simulate_preview_button_error(self):
        """模拟预览按钮错误情况"""
        with patch.object(self.main_window, 'get_selected_files', return_value=[]):
            self.main_window.preview_ppt()
    
    def _simulate_drag_drop_error(self):
        """模拟拖放错误情况"""
        mock_event = Mock(spec=QDropEvent)
        mock_mime_data = Mock(spec=QMimeData)
        mock_mime_data.hasUrls.return_value = False
        mock_event.mimeData.return_value = mock_mime_data
        
        self.main_window.dropEvent(mock_event)
    
    def test_function_call_tracing(self):
        """测试函数调用追踪 - 验证三个功能都调用preview_file"""
        # 创建调用追踪器
        call_tracker = []
        
        def track_preview_file(file_path):
            call_tracker.append(('preview_file', file_path))
            # 调用原始方法的简化版本
            self.main_window.create_api_key_manager()
            self.main_window.create_text_processor()
        
        # 替换preview_file方法
        original_preview_file = self.main_window.preview_file
        self.main_window.preview_file = track_preview_file
        
        try:
            # 测试拖放功能
            with patch.object(self.main_window, 'auto_detect_folders_from_file'):
                self._simulate_drag_drop()
            
            # 测试预览按钮功能
            self._simulate_preview_button()
            
            # 验证preview_file被调用
            self.assertEqual(len(call_tracker), 2)
            self.assertEqual(call_tracker[0][0], 'preview_file')
            self.assertEqual(call_tracker[1][0], 'preview_file')
            self.assertEqual(call_tracker[0][1], self.test_ppt_file)
            self.assertEqual(call_tracker[1][1], self.test_ppt_file)
            
        finally:
            # 恢复原始方法
            self.main_window.preview_file = original_preview_file
        
        self.logger.info("✓ 函数调用追踪测试通过")


class TestPreviewFunctionIntegration(unittest.TestCase):
    """预览功能集成测试类"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
        
        cls.logger = logging.getLogger(__name__)
    
    def setUp(self):
        """每个测试方法的初始化"""
        self.main_window = MainWindow()
    
    def tearDown(self):
        """每个测试方法的清理"""
        if hasattr(self, 'main_window'):
            self.main_window.close()
    
    def test_preview_functions_exist(self):
        """测试所有预览相关函数都存在"""
        required_functions = [
            'preview_file',
            'preview_ppt', 
            'dropEvent',
            'random_preview_unreviewed_file'
        ]
        
        for func_name in required_functions:
            with self.subTest(function=func_name):
                self.assertTrue(hasattr(self.main_window, func_name))
                self.assertTrue(callable(getattr(self.main_window, func_name)))
        
        self.logger.info("✓ 所有预览函数存在性测试通过")
    
    def test_error_handler_methods_exist(self):
        """测试错误处理器的所有方法都存在"""
        required_methods = [
            'validate_ppt_file_for_preview',
            'check_task_conflict',
            'validate_system_resources',
            'handle_error'
        ]
        
        for method_name in required_methods:
            with self.subTest(method=method_name):
                self.assertTrue(hasattr(ErrorHandler, method_name))
                self.assertTrue(callable(getattr(ErrorHandler, method_name)))
        
        self.logger.info("✓ 错误处理器方法存在性测试通过")


def run_functionality_tests():
    """运行功能测试"""
    print("=" * 60)
    print("开始预览功能统一化测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加功能测试
    test_suite.addTest(unittest.makeSuite(TestPreviewFunctionUnification))
    test_suite.addTest(unittest.makeSuite(TestPreviewFunctionIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 60)
    print("测试结果摘要")
    print("=" * 60)
    print(f"运行测试数量: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 返回测试是否成功
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_functionality_tests()
    sys.exit(0 if success else 1)