# PPT文本润色工具

这是一个使用AI技术对PPT文件中的文本内容进行润色和优化的工具。该工具可以保持文本的原意，同时提高文本表达的流畅度和自然度。

## 功能特点

- 批量处理多个PPT文件
- 自动识别并跳过处理英文文本和古诗文
- 保留文本的颜色格式
- 智能质量检查，确保润色后的文本与原文意思一致
- 支持自定义润色提示词
- 异步和并发处理，提高效率
- 提供美观的图形界面
- 实时显示处理进度
- 自动缓存处理结果，避免重复润色

## 系统要求

- Windows操作系统 (已在Windows 10上测试)
- Python 3.7或更高版本
- Microsoft PowerPoint (用于PPT到PPTX的转换)

## 安装

1. 克隆或下载本项目
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

3. 准备API密钥文件：创建一个文本文件，每行包含一个API密钥

## 使用方法

### 启动程序

```bash
python run.py
```

### 基本设置

1. **API设置**
   - API Keys文件：选择包含API密钥的文本文件
   - 提示词：自定义润色提示词
   - 最小文本长度：设置需要润色的最小文本长度

2. **文件选择**
   - 选择PPT文件：选择单个PPT文件处理
   - 选择PPT文件夹：选择包含多个PPT文件的文件夹
   - 输出文件夹：指定修改后文件的保存位置
   - 自动加载：从默认下载文件夹中加载最新的PPT文件
   - 拖放预览：将已修改的PPT文件拖放到指定区域可直接启动预览

3. **任务设置**
   - 幻灯片范围：指定要处理的幻灯片范围，例如"2-10 12 15-20"
   - 生成数量：为每个PPT文件生成的修改版本数量

### 预览功能

工具提供了三种预览方式来查看修改后的PPT文件：

1. **拖放预览**
   - 将已修改的PPT文件直接拖放到界面上的拖放区域
   - 系统会自动设置文件路径并启动预览窗口
   - 支持自动检测相关文件夹

2. **预览修改按钮**
   - 选择要预览的PPT文件后，点击"预览修改"按钮
   - 适用于查看当前选择文件的修改效果
   - 支持多文件选择时预览第一个文件

3. **随机预览未审阅文件**
   - 点击"随机预览一个未审阅文件"按钮
   - 从输出文件夹中随机选择一个未被审阅的文件进行预览
   - 帮助快速检查处理结果

### 开始处理

点击"开始修改"按钮开始处理PPT文件。处理过程中可以随时点击"停止"按钮中断处理。

处理完成后，程序会提示是否打开修改后的文件。

## 注意事项

- 请确保API密钥文件格式正确，每个密钥占一行
- 处理大型PPT文件或生成多个版本可能需要较长时间
- 程序会自动将PPT文件转换为PPTX格式
- 日志信息可在"日志"选项卡中查看
- selective类型的文件不能通过预览功能打开，请使用其他方式查看

## 故障排除

### 预览功能问题

**问题：拖放文件后没有反应**
- 确保拖放的是有效的PPT或PPTX文件
- 检查文件路径中是否包含特殊字符
- 查看日志选项卡中的错误信息

**问题：预览窗口无法打开**
- 确保API密钥文件已正确配置
- 检查系统内存是否充足
- 尝试重启程序后再次预览

**问题：预览修改按钮无效**
- 确保已选择至少一个PPT文件
- 检查选择的文件是否存在且可访问
- 确认文件格式为PPT或PPTX

**问题：随机预览找不到文件**
- 确保输出文件夹路径正确
- 检查输出文件夹中是否存在preview_开头的文件
- 确认文件夹权限允许程序访问

### 一般问题

**问题：程序启动失败**
- 检查Python版本是否为3.7或更高
- 确认所有依赖包已正确安装
- 查看控制台错误信息

**问题：处理速度慢**
- 检查网络连接状态
- 确认API密钥有效且未超出限制
- 考虑减少并发处理数量

## 高级配置

可以通过编辑配置文件调整高级设置：

- `.api_cache`：API请求缓存目录
- `.api_stats.json`：API使用统计信息

## 许可

本项目仅供学习和研究使用。 