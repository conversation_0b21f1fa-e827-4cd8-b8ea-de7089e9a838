"""
PPT文本润色工具主程序

提供PPT文本润色功能，支持批量处理PPT文件，使用AI技术提高文本质量
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication

from .gui.main_window import MainWindow


def setup_logging():
    """设置日志记录"""
    log_file = 'ppt_modifier.log'
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 添加文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(file_handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(console_handler)
    
    logging.info("日志系统初始化完成")


def main():
    """主程序入口"""
    # 设置日志记录
    setup_logging()
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 记录应用程序启动
    logging.info("PPT文本润色工具启动")
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    # 进入应用程序事件循环
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 