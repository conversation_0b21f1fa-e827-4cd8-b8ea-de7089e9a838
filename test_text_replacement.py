import os
import logging
import sys
from pptx import Presentation
from ppt_modifier.processor.ppt_processor import PPTProcessor
from ppt_modifier.api.api_key_manager import APIKeyManager
from ppt_modifier.processor.text_processor import TextProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_text_replacement():
    """测试手动修改的文本替换功能"""
    # 创建必要的依赖对象
    api_key_manager = APIKeyManager()
    text_processor = TextProcessor(api_key_manager)
    
    # 创建PPT处理器
    ppt_processor = PPTProcessor(api_key_manager, text_processor)
    
    # 测试数据
    original_text = """1、明日复明日，明日何其多!
2、少壮不努力，老大徒伤悲
3、人生在世，仅此一次，理应珍惜时光，活出真实，活出价值。——巴甫洛夫
4、一寸光阴一寸金，寸金难买寸光阴。
5、一万年太久，只争朝夕。 ——毛泽东"""

    modified_text = """1、明日复明日，明日何其多!
2、少壮不努力，老大徒伤悲
3、人生在世，仅此一次，理应珍惜时光，活出真实，活出价值。——巴甫洛夫
4、一寸光阴一寸金，寸金难买寸光阴。"""
    
    # 加载测试文件
    test_dir = "test_files"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建测试PPT文件路径
    test_file = os.path.join(test_dir, "test_replacement.pptx")
    output_file = os.path.join(test_dir, "test_replacement_output.pptx")
    
    # 查找项目目录中的示例文件
    example_files = [f for f in os.listdir(".") if f.endswith(".pptx")]
    if not example_files:
        logging.error("未找到PPTX示例文件，请将测试文件放在当前目录")
        return False
    
    example_file = example_files[0]
    logging.info(f"使用示例文件: {example_file}")
    
    # 复制示例文件作为测试文件
    prs = Presentation(example_file)
    
    # 保存测试文件
    prs.save(test_file)
    logging.info(f"已创建测试文件: {test_file}")
    
    # 模拟文本修改记录
    slide_idx = 1  # 第一张幻灯片
    ppt_processor.text_changes = {
        slide_idx: {
            'changes': [
                {
                    'id': '1',
                    'original': original_text,
                    'modified': modified_text,
                    'ai_modified': original_text,
                    'manually_edited': True,
                    'position': {
                        'x': 100000,
                        'y': 100000,
                        'width': 500000,
                        'height': 300000,
                        'relative_x': 0.1,
                        'relative_y': 0.1,
                        'relative_width': 0.5,
                        'relative_height': 0.3,
                        'slide_width': 9144000,
                        'slide_height': 6858000,
                        'rotation': 0.0
                    }
                }
            ]
        }
    }
    
    # 测试简化版选择性应用修改
    change_decisions = {
        slide_idx: {
            f"{slide_idx}_0": True  # 接受第一个修改
        }
    }
    
    try:
        # 复制测试文件进行测试
        import shutil
        shutil.copy2(test_file, output_file)
        
        # 测试直接修改函数
        logging.info("\n===== 测试直接文本搜索替换功能 =====")
        prs = Presentation(output_file)
        if prs.slides:
            slide = prs.slides[0]
            
            # 添加测试文本
            text_box = slide.shapes.add_textbox(
                100000, 100000, 500000, 300000
            )
            text_frame = text_box.text_frame
            text_frame.text = original_text
            
            # 保存修改
            prs.save(output_file)
            logging.info(f"已添加测试文本框: {original_text}")
            
            # 重新加载
            prs = Presentation(output_file)
            slide = prs.slides[0]
            
            # 收集形状
            shapes = []
            ppt_processor._collect_all_shapes_with_text(slide, shapes)
            
            # 测试模糊更新
            result1 = ppt_processor._fuzzy_update_text(shapes, original_text, modified_text)
            logging.info(f"模糊更新结果: {result1}")
            
            # 保存修改
            prs.save(output_file)
            logging.info(f"已保存模糊更新结果到: {output_file}")
            
            # 重新加载验证
            prs = Presentation(output_file)
            slide = prs.slides[0]
            shapes = []
            ppt_processor._collect_all_shapes_with_text(slide, shapes)
            
            # 验证文本是否已正确更新
            if shapes and hasattr(shapes[0], 'text_frame'):
                text_after = shapes[0].text_frame.text.strip()
                logging.info(f"更新后的文本: {text_after}")
                if text_after == modified_text.strip():
                    logging.info("✅ 文本更新成功！")
                else:
                    logging.error("❌ 文本更新失败！")
                    logging.error(f"期望: {modified_text.strip()}")
                    logging.error(f"实际: {text_after}")
            else:
                logging.error("找不到文本形状以进行验证")
        
        # 测试选择性应用修改
        logging.info("\n===== 测试选择性应用修改 =====")
        try:
            result_path = ppt_processor.simple_selective_changes(
                output_file,
                os.path.join(test_dir, "test_selective_output.pptx"),
                change_decisions
            )
            logging.info(f"选择性应用修改结果: {result_path}")
        except Exception as e:
            logging.error(f"选择性应用修改失败: {str(e)}")
        
        logging.info("测试完成")
        return True
    except Exception as e:
        logging.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_text_replacement() 