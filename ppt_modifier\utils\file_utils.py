"""
文件工具函数模块
提供文件操作相关的工具函数
"""

import os
import logging
import shutil
import subprocess
from pathlib import Path
from typing import Optional, List


def get_latest_folder(folder_path: str) -> Optional[str]:
    """
    获取指定目录下最新创建的子目录
    
    Args:
        folder_path: 要搜索的目录路径
        
    Returns:
        最新子目录的路径，如果没有则返回None
    """
    if not os.path.exists(folder_path):
        logging.warning(f"目录不存在: {folder_path}")
        return None
    
    try:
        # 使用pathlib查找所有子目录并按修改时间排序
        subfolders = [f for f in Path(folder_path).iterdir() if f.is_dir()]
        if not subfolders:
            return None
            
        latest = max(subfolders, key=lambda x: x.stat().st_mtime)
        return str(latest)
    except Exception as e:
        logging.error(f"获取最新目录时出错: {str(e)}")
        return None


def get_latest_file(folder_path: str, extensions: List[str] = ['.ppt', '.pptx']) -> Optional[str]:
    """
    获取指定目录下最新创建的文件(按指定扩展名筛选)
    
    Args:
        folder_path: 要搜索的目录路径
        extensions: 要筛选的文件扩展名列表
        
    Returns:
        最新文件的路径，如果没有则返回None
    """
    if not os.path.exists(folder_path):
        logging.warning(f"目录不存在: {folder_path}")
        return None
    
    try:
        # 确保扩展名都是小写的
        extensions = [ext.lower() for ext in extensions]
        
        # 查找所有符合扩展名的文件
        matching_files = [
            f for f in Path(folder_path).iterdir() 
            if f.is_file() and f.suffix.lower() in extensions
        ]
        
        if not matching_files:
            return None
            
        # 按修改时间排序，返回最新的
        latest = max(matching_files, key=lambda x: x.stat().st_mtime)
        return str(latest)
    except Exception as e:
        logging.error(f"获取最新文件时出错: {str(e)}")
        return None


def open_file(file_path: str) -> bool:
    """
    使用系统默认程序打开文件
    
    Args:
        file_path: 要打开的文件路径
        
    Returns:
        是否成功打开文件
    """
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return False
        
    try:
        if os.name == 'nt':  # Windows
            os.startfile(file_path)
        elif os.name == 'posix':  # macOS/Linux
            subprocess.run(['xdg-open', file_path], check=True)
        else:
            logging.error(f"不支持的操作系统: {os.name}")
            return False
        return True
    except Exception as e:
        logging.error(f"打开文件时出错: {str(e)}")
        return False


def copy_file(source_path: str, destination_path: str) -> bool:
    """
    复制文件
    
    Args:
        source_path: 源文件路径
        destination_path: 目标文件路径
        
    Returns:
        是否成功复制文件
    """
    if not os.path.exists(source_path):
        logging.error(f"源文件不存在: {source_path}")
        return False
        
    try:
        # 确保目标目录存在
        destination_dir = os.path.dirname(destination_path)
        if destination_dir and not os.path.exists(destination_dir):
            os.makedirs(destination_dir, exist_ok=True)
            
        # 复制文件
        shutil.copy2(source_path, destination_path)
        logging.info(f"文件复制成功: {source_path} -> {destination_path}")
        return True
    except Exception as e:
        logging.error(f"复制文件时出错: {str(e)}")
        return False 