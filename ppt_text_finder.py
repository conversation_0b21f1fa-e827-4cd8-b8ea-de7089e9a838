import os
import re
import difflib
import logging
from pptx import Presentation
import jieba  # 中文分词库

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

class PPTTextFinder:
    """PPT文本查找工具，使用多种策略找到指定文本"""
    
    def __init__(self, ppt_path):
        """初始化查找器
        
        Args:
            ppt_path: PPT文件路径
        """
        self.ppt_path = ppt_path
        self.prs = None
        self.load_presentation()
    
    def load_presentation(self):
        """加载PPT文件"""
        try:
            self.prs = Presentation(self.ppt_path)
            logging.info(f"成功加载PPT文件: {self.ppt_path}")
            logging.info(f"PPT包含 {len(self.prs.slides)} 张幻灯片")
        except Exception as e:
            logging.error(f"无法加载PPT文件: {str(e)}")
            raise
    
    def normalize_text(self, text):
        """标准化文本，去除多余空格"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text).strip()
    
    def simplify_text(self, text):
        """简化文本，去除标点符号和空格，转为小写"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text).strip()
        text = re.sub(r'[,.;:!?，。；：！？]', '', text)
        return text.lower()
    
    def get_keywords(self, text, top_n=8):
        """提取文本中的关键词
        
        Args:
            text: 要提取关键词的文本
            top_n: 返回前N个关键词
            
        Returns:
            关键词列表
        """
        # 使用jieba分词提取关键词
        words = jieba.lcut(text)
        # 过滤掉停用词和单字词
        keywords = [word for word in words if len(word) > 1]
        # 返回前N个关键词
        return keywords[:top_n]
    
    def calculate_word_match_ratio(self, text1, text2):
        """计算两段文本的词语匹配率
        
        Returns:
            匹配率(0-1)
        """
        words1 = set(jieba.lcut(text1))
        words2 = set(jieba.lcut(text2))
        
        if not words1:
            return 0
            
        # 计算交集大小除以第一段文本词数
        return len(words1.intersection(words2)) / len(words1)
    
    def find_text(self, target_text):
        """在PPT中查找指定文本，使用多种匹配策略
        
        Args:
            target_text: 要查找的目标文本
            
        Returns:
            匹配结果列表，每个元素为字典，包含幻灯片索引、形状对象、匹配度等信息
        """
        if not self.prs:
            logging.error("PPT文件未加载")
            return []
            
        # 准备目标文本的各种形式
        normalized_target = self.normalize_text(target_text)
        simplified_target = self.simplify_text(target_text)
        keywords = self.get_keywords(target_text)
        
        logging.info(f"查找目标文本: '{target_text}'")
        logging.info(f"标准化后: '{normalized_target}'")
        logging.info(f"简化后: '{simplified_target}'")
        logging.info(f"关键词: {keywords}")
        
        results = []
        
        # 遍历所有幻灯片和形状
        for slide_idx, slide in enumerate(self.prs.slides):
            logging.info(f"检查幻灯片 {slide_idx+1}/{len(self.prs.slides)}")
            
            for shape_idx, shape in enumerate(slide.shapes):
                if not hasattr(shape, 'text_frame'):
                    continue
                    
                text_frame = shape.text_frame
                if not text_frame.text:
                    continue
                
                # 获取文本内容及其各种形式
                full_text = text_frame.text
                normalized_full = self.normalize_text(full_text)
                simplified_full = self.simplify_text(full_text)
                
                # 标记此形状是否包含段落
                has_paragraphs = len(text_frame.paragraphs) > 0
                para_texts = [p.text for p in text_frame.paragraphs if p.text.strip()]
                
                # 多种匹配策略
                
                # 1. 精确匹配
                exact_match = (normalized_target in normalized_full)
                
                # 2. 相似度匹配
                similarity = difflib.SequenceMatcher(None, normalized_target, normalized_full).ratio()
                
                # 3. 关键词匹配率
                keyword_match_ratio = self.calculate_word_match_ratio(target_text, full_text)
                
                # 4. 段落级匹配
                para_matches = []
                for idx, para_text in enumerate(para_texts):
                    para_similarity = difflib.SequenceMatcher(None, normalized_target, self.normalize_text(para_text)).ratio()
                    if para_similarity > 0.7 or normalized_target in self.normalize_text(para_text):
                        para_matches.append({
                            'index': idx,
                            'text': para_text,
                            'similarity': para_similarity
                        })
                
                # 根据多种策略计算综合分数
                score = (
                    (1.0 if exact_match else 0.0) * 0.4 +  # 精确匹配权重0.4
                    similarity * 0.3 +                      # 相似度权重0.3
                    keyword_match_ratio * 0.3               # 关键词匹配权重0.3
                )
                
                # 如果分数超过阈值或有高相似度的段落匹配，记录结果
                if score > 0.5 or para_matches:
                    result = {
                        'slide_idx': slide_idx + 1,
                        'shape_idx': shape_idx,
                        'text': full_text,
                        'score': score,
                        'exact_match': exact_match,
                        'similarity': similarity,
                        'keyword_match': keyword_match_ratio,
                        'para_matches': para_matches,
                        'shape': shape
                    }
                    results.append(result)
                    
                    logging.info(f"找到潜在匹配，分数: {score:.2f}")
                    logging.info(f"文本: '{full_text[:100]}{'...' if len(full_text)>100 else ''}'")
                    if para_matches:
                        logging.info(f"匹配段落数: {len(para_matches)}")
        
        # 按匹配分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        logging.info(f"共找到 {len(results)} 个潜在匹配")
        
        # 输出详细结果
        for i, result in enumerate(results[:3]):  # 只显示前3个最佳匹配
            logging.info(f"匹配 #{i+1} - 幻灯片 {result['slide_idx']}, 分数: {result['score']:.2f}")
            logging.info(f"文本: '{result['text'][:100]}{'...' if len(result['text'])>100 else ''}'")
            logging.info(f"精确匹配: {result['exact_match']}, 相似度: {result['similarity']:.2f}, 关键词匹配: {result['keyword_match']:.2f}")
            
            if result['para_matches']:
                for pm in result['para_matches']:
                    logging.info(f"段落匹配 #{pm['index']}: '{pm['text'][:50]}{'...' if len(pm['text'])>50 else ''}' - 相似度: {pm['similarity']:.2f}")
        
        return results
    
    def print_text_frames(self, slide_idx=None):
        """打印幻灯片中的所有文本框内容，用于分析
        
        Args:
            slide_idx: 可选，指定幻灯片索引(从1开始)，如果为None则打印所有幻灯片
        """
        if not self.prs:
            logging.error("PPT文件未加载")
            return
            
        if slide_idx is not None:
            if slide_idx < 1 or slide_idx > len(self.prs.slides):
                logging.error(f"幻灯片索引超出范围: {slide_idx}")
                return
                
            slides = [self.prs.slides[slide_idx-1]]
            logging.info(f"打印幻灯片 {slide_idx} 的文本框内容")
        else:
            slides = self.prs.slides
            logging.info(f"打印所有 {len(slides)} 张幻灯片的文本框内容")
        
        for i, slide in enumerate(slides):
            current_slide_idx = slide_idx or (i + 1)
            logging.info(f"------ 幻灯片 {current_slide_idx} ------")
            
            for j, shape in enumerate(slide.shapes):
                if hasattr(shape, 'text_frame'):
                    text = shape.text_frame.text.strip()
                    if text:
                        logging.info(f"文本框 #{j+1}: '{text[:100]}{'...' if len(text)>100 else ''}'")
                        
                        # 打印每个段落
                        for k, para in enumerate(shape.text_frame.paragraphs):
                            para_text = para.text.strip()
                            if para_text:
                                logging.info(f"  段落 #{k+1}: '{para_text[:80]}{'...' if len(para_text)>80 else ''}'")
                                
                                # 打印每个run
                                for m, run in enumerate(para.runs):
                                    run_text = run.text.strip()
                                    if run_text:
                                        logging.info(f"    Run #{m+1}: '{run_text[:50]}{'...' if len(run_text)>50 else ''}'")

def main():
    """主函数"""
    # 获取文件路径
    ppt_path = input("请输入PPT文件路径: ").strip()
    if not os.path.exists(ppt_path):
        logging.error(f"文件不存在: {ppt_path}")
        return
    
    # 初始化查找器
    finder = PPTTextFinder(ppt_path)
    
    while True:
        print("\n===== PPT文本查找工具 =====")
        print("1. 查找文本")
        print("2. 打印指定幻灯片所有文本框内容")
        print("3. 打印所有幻灯片文本框内容")
        print("0. 退出")
        
        choice = input("请选择操作 [0-3]: ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            target_text = input("请输入要查找的文本: ").strip()
            finder.find_text(target_text)
        elif choice == '2':
            try:
                slide_idx = int(input("请输入幻灯片索引(从1开始): ").strip())
                finder.print_text_frames(slide_idx)
            except ValueError:
                logging.error("请输入有效的幻灯片索引数字")
        elif choice == '3':
            finder.print_text_frames()
        else:
            print("无效的选择，请重试")

if __name__ == "__main__":
    main() 