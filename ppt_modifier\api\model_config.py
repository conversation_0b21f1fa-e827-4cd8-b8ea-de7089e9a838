"""
模型配置管理模块
提供AI模型配置的加载和保存功能
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

class ModelConfigFile:
    """模型配置文件管理"""
    
    DEFAULT_CONFIG = {
        "providers": {
            "yi": {
                "name": "Yi",
                "base_url": "https://api.lingyiwanwu.com/v1",
                "models": [
                    {
                        "id": "yi-lightning",
                        "name": "Yi-Lightning",
                        "temperature": 0.3
                    }
                ]
            },
            "openrouter": {
                "name": "OpenRouter",
                "base_url": "https://openrouter.ai/api/v1",
                "models": [
                    {
                        "id": "anthropic/claude-3-opus",
                        "name": "Claude 3 Opus",
                        "temperature": 0.3
                    },
                    {
                        "id": "anthropic/claude-3-sonnet",
                        "name": "Claude 3 Sonnet",
                        "temperature": 0.3
                    },
                    {
                        "id": "google/gemini-pro",
                        "name": "Google Gemini Pro",
                        "temperature": 0.3
                    },
                    {
                        "id": "meta-llama/llama-3-70b",
                        "name": "Meta Llama 3 70B",
                        "temperature": 0.3
                    },
                    {
                        "id": "openai/gpt-4o",
                        "name": "GPT-4o",
                        "temperature": 0.3
                    },
                    {
                        "id": "openai/gpt-4-turbo",
                        "name": "GPT-4 Turbo",
                        "temperature": 0.3
                    },
                    {
                        "id": "mistralai/mistral-large",
                        "name": "Mistral Large",
                        "temperature": 0.3
                    }
                ]
            }
        }
    }
    
    def __init__(self, config_path="model_config.json"):
        """初始化配置文件管理器"""
        self.config_path = config_path
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件，如果不存在则创建默认配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认配置文件
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(self.DEFAULT_CONFIG, f, indent=2, ensure_ascii=False)
                return self.DEFAULT_CONFIG
        except Exception as e:
            logging.error(f"加载模型配置文件失败: {str(e)}")
            return self.DEFAULT_CONFIG
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"保存模型配置文件失败: {str(e)}")
            return False
    
    def get_providers(self):
        """获取所有提供商"""
        return self.config.get("providers", {})
    
    def get_provider(self, provider_id):
        """获取指定提供商"""
        return self.config.get("providers", {}).get(provider_id)
    
    def get_models(self, provider_id):
        """获取指定提供商的所有模型"""
        provider = self.get_provider(provider_id)
        return provider.get("models", []) if provider else []
    
    def add_model(self, provider_id, model_id, model_name, temperature=0.3):
        """添加模型到指定提供商"""
        provider = self.get_provider(provider_id)
        if provider:
            # 检查是否已存在相同ID的模型
            for model in provider.get("models", []):
                if model.get("id") == model_id:
                    model["name"] = model_name
                    model["temperature"] = temperature
                    return True
                    
            # 不存在则添加新模型
            provider.setdefault("models", []).append({
                "id": model_id,
                "name": model_name,
                "temperature": temperature
            })
            return True
        return False
        
    def delete_model(self, provider_id, model_id):
        """删除指定提供商的模型"""
        provider = self.get_provider(provider_id)
        if provider and "models" in provider:
            provider["models"] = [m for m in provider["models"] if m.get("id") != model_id]
            return True
        return False


class ModelConfigManager:
    """模型配置管理器"""
    def __init__(self, settings, config_file):
        # 保存配置文件引用
        self.config_file = config_file
        
        # 从设置中加载之前保存的配置
        self.active_provider = settings.value("model_provider", "yi", str)
        self.active_model = settings.value("model_id", "yi-lightning", str)
        self.openrouter_api_key = settings.value("openrouter_api_key", "", str)
        self.openrouter_site_url = settings.value("openrouter_site_url", "", str)
        self.openrouter_site_name = settings.value("openrouter_site_name", "PPT文本润色工具", str)
        
    def get_current_provider_config(self):
        """获取当前提供商配置"""
        return self.config_file.get_provider(self.active_provider)
        
    def get_current_model_config(self):
        """获取当前模型配置"""
        provider = self.get_current_provider_config()
        if provider:
            logging.info(f"当前提供商: {self.active_provider}, 当前模型ID: {self.active_model}")
            
            for model in provider.get("models", []):
                model_id = model.get("id", "")
                if model_id == self.active_model:
                    logging.info(f"找到匹配的模型配置: {model_id}")
                    return model
                
            # 如果找不到匹配的模型，记录警告并尝试使用第一个可用模型
            if provider.get("models"):
                first_model = provider.get("models")[0]
                first_model_id = first_model.get("id", "")
                logging.warning(f"未找到匹配的模型配置: {self.active_model}，回退使用第一个可用模型: {first_model_id}")
                # 自动更新当前模型ID为第一个模型的ID
                self.active_model = first_model_id
                return first_model
                
        logging.error(f"未找到有效的模型配置，提供商: {self.active_provider}, 模型ID: {self.active_model}")
        return None
    
    def get_api_key(self, api_key_manager):
        """获取API密钥"""
        if self.active_provider == "openrouter":
            return self.openrouter_api_key
        else:
            return api_key_manager.get_next_key()
            
    def set_active_model(self, provider_id, model_id):
        """设置当前激活的模型"""
        self.active_provider = provider_id
        self.active_model = model_id 
        
    def update_provider_settings(self, provider, openrouter_api_key="", openrouter_site_url="", openrouter_site_name="PPT文本润色工具"):
        """更新提供商设置"""
        # 如果提供商改变，记录日志
        if self.active_provider != provider:
            logging.info(f"提供商变更: {self.active_provider} -> {provider}")
            
        # 保存原来的active_model
        old_active_model = self.active_model
            
        # 更新提供商
        self.active_provider = provider
        
        # 不自动更新active_model，保持用户之前的选择
        # 仅在get_current_model_config方法中，如果找不到匹配的模型时才会自动修正
        
        # 更新OpenRouter特定设置
        if provider == "openrouter":
            self.openrouter_api_key = openrouter_api_key
            self.openrouter_site_url = openrouter_site_url
            self.openrouter_site_name = openrouter_site_name
            
        # 记录日志
        logging.info(f"更新提供商设置完成 - 提供商: {provider}, 模型ID: {self.active_model}") 