#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的一致性测试脚本 - 验证三个预览功能的一致性
测试任务 6.2: 验证三个功能使用相同的preview_file()函数、预览窗口创建的一致性、错误处理的一致性
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMessageBox
from ppt_modifier.gui.main_window import MainWindow


def test_consistency():
    """执行一致性测试"""
    print("=" * 80)
    print("开始执行预览功能一致性测试")
    print("=" * 80)
    
    # 创建QApplication实例
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    
    # 创建临时目录和测试文件
    test_dir = tempfile.mkdtemp()
    test_ppt_file = os.path.join(test_dir, "test.pptx")
    selective_ppt_file = os.path.join(test_dir, "test_selective.pptx")
    
    try:
        # 创建测试文件
        for file_path in [test_ppt_file, selective_ppt_file]:
            with open(file_path, 'wb') as f:
                f.write(b'fake ppt content')
        
        # 创建主窗口实例
        main_window = MainWindow()
        
        # 设置必要的配置
        api_keys_file = os.path.join(test_dir, "api_keys.txt")
        with open(api_keys_file, 'w') as f:
            f.write("test_api_key")
        main_window.api_keys_edit.setText(api_keys_file)
        main_window.output_path_edit.setText(test_dir)
        
        print("✓ 测试环境设置完成")
        
        # 测试1: 验证三个功能都使用相同的preview_file()函数
        print("\n1. 测试preview_file函数一致性...")
        test_preview_file_consistency(main_window, test_ppt_file)
        
        # 测试2: 验证预览窗口创建的一致性
        print("\n2. 测试预览窗口创建一致性...")
        test_preview_window_consistency(main_window, test_ppt_file)
        
        # 测试3: 验证错误处理的一致性
        print("\n3. 测试错误处理一致性...")
        test_error_handling_consistency(main_window, test_dir, selective_ppt_file)
        
        # 测试4: 验证函数存在性
        print("\n4. 测试函数存在性...")
        test_function_existence(main_window)
        
        print("\n" + "=" * 80)
        print("✓ 所有一致性测试通过！")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理
        try:
            main_window.close()
            shutil.rmtree(test_dir)
        except:
            pass


def test_preview_file_consistency(main_window, test_ppt_file):
    """测试三个功能都使用相同的preview_file()函数"""
    call_tracker = []
    
    def track_preview_file(file_path):
        call_tracker.append(('preview_file', file_path))
        # 模拟preview_file的基本行为，避免实际创建窗口
        return
    
    # 替换preview_file方法
    original_preview_file = main_window.preview_file
    main_window.preview_file = track_preview_file
    
    try:
        # 1. 测试拖放功能调用preview_file
        simulate_drag_drop(main_window, test_ppt_file)
        
        # 2. 测试预览修改按钮调用preview_file
        simulate_preview_button(main_window, test_ppt_file)
        
        # 验证preview_file被调用
        assert len(call_tracker) == 2, f"preview_file应该被调用2次，实际调用{len(call_tracker)}次"
        assert call_tracker[0][0] == 'preview_file', "第一次调用应该是preview_file"
        assert call_tracker[1][0] == 'preview_file', "第二次调用应该是preview_file"
        
        # 验证调用的文件路径正确
        assert call_tracker[0][1] == test_ppt_file, "拖放功能应该调用正确的文件路径"
        assert call_tracker[1][1] == test_ppt_file, "预览按钮功能应该调用正确的文件路径"
        
        print("  ✓ 拖放功能和预览修改按钮都调用了preview_file函数")
        print("  ✓ 调用的文件路径参数一致")
        
    finally:
        # 恢复原始方法
        main_window.preview_file = original_preview_file


def test_preview_window_consistency(main_window, test_ppt_file):
    """测试预览窗口创建的一致性"""
    
    # Mock预览窗口创建相关方法
    mock_preview_window = Mock()
    mock_preview_window.isVisible.return_value = False
    mock_preview_window.show = Mock()
    mock_preview_window.raise_ = Mock()
    mock_preview_window.activateWindow = Mock()
    mock_preview_window.setFocus = Mock()
    mock_preview_window.setWindowTitle = Mock()
    mock_preview_window.load_ppt = Mock()
    
    with patch.object(main_window, '_create_preview_window', return_value=mock_preview_window) as mock_create:
        with patch.object(main_window, '_cleanup_preview_window') as mock_cleanup:
            with patch.object(main_window, '_show_preview_window') as mock_show:
                with patch.object(main_window, '_set_preview_window_title') as mock_title:
                    
                    # 测试直接调用preview_file
                    main_window.preview_file(test_ppt_file)
                    
                    # 验证统一的预览窗口创建流程
                    mock_cleanup.assert_called_once()
                    mock_create.assert_called_once()
                    mock_show.assert_called_once()
                    mock_title.assert_called_once()
                    
                    # 验证创建参数的一致性
                    create_args = mock_create.call_args
                    assert len(create_args[0]) == 2, "创建预览窗口应该传入2个参数"
                    
                    # 验证参数类型
                    api_manager, text_processor = create_args[0]
                    assert api_manager is not None, "API管理器不能为空"
                    assert text_processor is not None, "文本处理器不能为空"
    
    print("  ✓ 预览窗口创建流程一致：清理 -> 创建 -> 显示 -> 设置标题")
    print("  ✓ 预览窗口创建参数一致：API管理器和文本处理器")


def test_error_handling_consistency(main_window, test_dir, selective_ppt_file):
    """测试错误处理的一致性"""
    
    # 测试文件不存在的错误处理
    non_existent_file = os.path.join(test_dir, "non_existent.pptx")
    
    with patch.object(QMessageBox, 'warning') as mock_warning:
        with patch.object(QMessageBox, 'critical') as mock_critical:
            
            # 测试preview_file的错误处理
            main_window.preview_file(non_existent_file)
            
            # 验证错误处理被调用
            error_shown = mock_warning.called or mock_critical.called
            assert error_shown, "应该显示错误消息"
    
    print("  ✓ 文件不存在时显示错误消息")
    
    # 测试selective文件的错误处理
    with patch.object(QMessageBox, 'warning') as mock_warning:
        with patch.object(QMessageBox, 'critical') as mock_critical:
            
            # 测试preview_file对selective文件的处理
            main_window.preview_file(selective_ppt_file)
            
            # 验证selective文件错误处理（可能是warning或critical）
            error_shown = mock_warning.called or mock_critical.called
            assert error_shown, "应该显示错误消息"
            
            # 验证错误消息内容包含selective相关信息
            if mock_warning.called:
                warning_args = mock_warning.call_args
                error_message = warning_args[0][2]  # 第三个参数是错误消息
            else:
                critical_args = mock_critical.call_args
                error_message = critical_args[0][2]  # 第三个参数是错误消息
            
            assert "selective" in error_message.lower(), "错误消息应该包含selective相关信息"
    
    print("  ✓ selective文件被正确拒绝并显示相应错误消息")


def test_function_existence(main_window):
    """测试函数存在性"""
    
    # 验证关键函数存在
    required_functions = [
        'preview_file',
        'preview_ppt', 
        'dropEvent',
        '_create_preview_window',
        '_cleanup_preview_window',
        '_show_preview_window',
        '_set_preview_window_title',
        'create_api_key_manager',
        'create_text_processor'
    ]
    
    for func_name in required_functions:
        assert hasattr(main_window, func_name), f"主窗口应该有 {func_name} 方法"
        assert callable(getattr(main_window, func_name)), f"{func_name} 应该是可调用的"
    
    print(f"  ✓ 所有必需的函数都存在且可调用 ({len(required_functions)}个)")
    
    # 验证preview_file函数签名
    import inspect
    preview_file_sig = inspect.signature(main_window.preview_file)
    assert len(preview_file_sig.parameters) == 1, "preview_file应该接受1个参数（file_path）"
    
    param_name = list(preview_file_sig.parameters.keys())[0]
    assert param_name == 'file_path', "preview_file的参数应该命名为file_path"
    
    print("  ✓ preview_file函数签名正确")


def simulate_drag_drop(main_window, test_ppt_file):
    """模拟拖放操作"""
    # 创建模拟的拖放事件
    mock_event = Mock()
    mock_mime_data = Mock()
    mock_url = Mock()
    
    mock_url.toLocalFile.return_value = test_ppt_file
    mock_mime_data.hasUrls.return_value = True
    mock_mime_data.urls.return_value = [mock_url]
    mock_event.mimeData.return_value = mock_mime_data
    mock_event.acceptProposedAction = Mock()
    
    # 执行拖放
    main_window.dropEvent(mock_event)


def simulate_preview_button(main_window, test_ppt_file):
    """模拟预览修改按钮操作"""
    # 设置选择的文件
    with patch.object(main_window, 'get_selected_files', return_value=[test_ppt_file]):
        main_window.preview_ppt()


if __name__ == "__main__":
    success = test_consistency()
    sys.exit(0 if success else 1)