"""
主窗口模块
提供图形用户界面的主窗口实现
"""

import os
import logging
import sys
import json
import time
import glob  # 添加这一行用于文件查找
import re
import random  # 添加random模块用于随机选择文件
from typing import Optional, List, Dict, Any, Tuple
from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, \
    QFileDialog, QMessageBox, QProgressBar, QGroupBox, QApplication, QTabWidget, QTextEdit, QSpinBox, QProgressDialog, \
    QComboBox, QDialog
from PyQt5.QtCore import Qt, QSettings, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont, QPixmap, QTextCursor
from pptx import Presentation

from ..api.api_manager import APIKeyManager
from ..processor.text_processor import TextProcessor
from ..processor.ppt_processor import PPTProcessor
from ..utils.file_utils import get_latest_folder, get_latest_file, open_file, copy_file
from ..api.model_config import ModelConfigFile, ModelConfigManager
from .preview_window import PreviewWindow  # 导入预览窗口类
from .model_config_dialog import ModelConfigDialog  # 导入模型配置对话框


class PPTModifierError(Exception):
    """PPT修改工具基础异常类"""
    pass


class FileValidationError(PPTModifierError):
    """文件验证错误"""
    pass


class TaskConflictError(PPTModifierError):
    """任务冲突错误"""
    pass


class SystemResourceError(PPTModifierError):
    """系统资源错误"""
    pass


class ErrorHandler:
    """统一错误处理器 - 增强版本，保持向后兼容性"""
    
    @staticmethod
    def validate_file_path(file_path: str, check_exists: bool = True) -> None:
        """
        标准化文件路径验证
        
        Args:
            file_path: 文件路径
            check_exists: 是否检查文件存在性
            
        Raises:
            FileValidationError: 文件验证失败
        """
        if not file_path or not isinstance(file_path, str):
            raise FileValidationError("文件路径无效")
        
        if not file_path.strip():
            raise FileValidationError("文件路径不能为空")
        
        if check_exists and not os.path.exists(file_path):
            raise FileValidationError(f"文件不存在: {file_path}")
        
        # 检查文件格式
        if not file_path.lower().endswith(('.ppt', '.pptx')):
            ext = os.path.splitext(file_path)[1] or "无扩展名"
            raise FileValidationError(f"不支持的文件格式: {ext}")
        
        # 检查文件是否可读
        if check_exists:
            try:
                with open(file_path, 'rb') as f:
                    f.read(1)  # 尝试读取一个字节
            except PermissionError:
                raise FileValidationError(f"文件无法访问，权限不足: {file_path}")
            except Exception as e:
                raise FileValidationError(f"文件无法读取: {str(e)}")
    
    @staticmethod
    def validate_folder_path(folder_path: str, check_exists: bool = True) -> None:
        """
        标准化文件夹路径验证
        
        Args:
            folder_path: 文件夹路径
            check_exists: 是否检查文件夹存在性
            
        Raises:
            FileValidationError: 文件夹验证失败
        """
        if not folder_path or not isinstance(folder_path, str):
            raise FileValidationError("文件夹路径无效")
        
        if not folder_path.strip():
            raise FileValidationError("文件夹路径不能为空")
        
        if check_exists and not os.path.exists(folder_path):
            raise FileValidationError(f"文件夹不存在: {folder_path}")
        
        if check_exists and not os.path.isdir(folder_path):
            raise FileValidationError(f"路径不是有效的文件夹: {folder_path}")
        
        # 检查文件夹是否可访问
        if check_exists:
            try:
                os.listdir(folder_path)
            except PermissionError:
                raise FileValidationError(f"文件夹无法访问，权限不足: {folder_path}")
            except Exception as e:
                raise FileValidationError(f"文件夹无法访问: {str(e)}")
    
    @staticmethod
    def validate_api_keys_file(api_keys_file: str) -> None:
        """
        标准化API密钥文件验证
        
        Args:
            api_keys_file: API密钥文件路径
            
        Raises:
            FileValidationError: API密钥文件验证失败
        """
        if not api_keys_file or not isinstance(api_keys_file, str):
            raise FileValidationError("API密钥文件路径无效")
        
        if not api_keys_file.strip():
            raise FileValidationError("API密钥文件路径不能为空")
        
        if not os.path.exists(api_keys_file):
            raise FileValidationError(f"API密钥文件不存在: {api_keys_file}")
        
        if not os.path.isfile(api_keys_file):
            raise FileValidationError(f"API密钥路径不是有效的文件: {api_keys_file}")
        
        # 检查文件是否可读且格式正确
        try:
            with open(api_keys_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    raise FileValidationError(f"API密钥文件为空: {api_keys_file}")
        except PermissionError:
            raise FileValidationError(f"API密钥文件无法访问，权限不足: {api_keys_file}")
        except UnicodeDecodeError:
            raise FileValidationError(f"API密钥文件编码格式不正确: {api_keys_file}")
        except Exception as e:
            raise FileValidationError(f"API密钥文件读取失败: {str(e)}")
    
    @staticmethod
    def check_task_conflict(worker_thread) -> None:
        """
        统一任务冲突检查
        
        Args:
            worker_thread: 工作线程对象
            
        Raises:
            TaskConflictError: 存在任务冲突
        """
        if worker_thread and worker_thread.isRunning():
            raise TaskConflictError("请等待当前任务完成后再执行新任务")
    
    @staticmethod
    def check_preview_window_conflict(preview_window) -> None:
        """
        检查预览窗口冲突
        
        Args:
            preview_window: 预览窗口对象
            
        Raises:
            TaskConflictError: 存在预览窗口冲突
        """
        if preview_window and hasattr(preview_window, 'isVisible') and preview_window.isVisible():
            # 不抛出异常，而是记录日志，因为预览窗口可以被替换
            logging.info("检测到已存在的预览窗口，将被新窗口替换")
    
    @staticmethod
    def validate_system_resources() -> None:
        """
        标准化系统资源检查
        
        Raises:
            SystemResourceError: 系统资源不足
        """
        try:
            # 检查可用内存（简单检查）
            import psutil
            memory = psutil.virtual_memory()
            if memory.percent > 95:  # 内存使用率超过95%
                raise SystemResourceError(f"系统内存不足，当前使用率: {memory.percent:.1f}%")
            
            # 检查可用磁盘空间
            disk = psutil.disk_usage('.')
            if disk.percent > 98:  # 磁盘使用率超过98%
                raise SystemResourceError(f"磁盘空间不足，当前使用率: {disk.percent:.1f}%")
                
        except ImportError:
            # 如果没有psutil模块，跳过系统资源检查
            logging.debug("未安装psutil模块，跳过系统资源检查")
        except Exception as e:
            logging.warning(f"系统资源检查失败: {str(e)}")
    
    @staticmethod
    def handle_error(parent_widget, error: Exception, operation: str = "操作") -> None:
        """
        统一错误处理和用户提示 - 保持向后兼容的错误消息格式
        
        Args:
            parent_widget: 父窗口组件
            error: 异常对象
            operation: 操作描述
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # 记录详细错误日志
        logging.error(f"{operation}失败 - {error_type}: {error_message}")
        
        # 保持向后兼容的错误提示格式和标题
        if isinstance(error, FileValidationError):
            # 保持原有的错误提示格式
            if "文件不存在" in error_message:
                QMessageBox.warning(parent_widget, "警告", error_message)
            elif "不支持的文件格式" in error_message:
                QMessageBox.warning(parent_widget, "警告", error_message)
            elif "权限不足" in error_message:
                QMessageBox.critical(parent_widget, "错误", error_message)
            else:
                QMessageBox.critical(parent_widget, "文件错误", f"{operation}失败:\n{error_message}")
        elif isinstance(error, TaskConflictError):
            # 保持原有的任务冲突提示格式
            QMessageBox.warning(parent_widget, "任务冲突", f"{operation}失败:\n{error_message}")
        elif isinstance(error, SystemResourceError):
            # 保持原有的系统资源错误提示格式
            QMessageBox.critical(parent_widget, "系统资源不足", f"{operation}失败:\n{error_message}")
        elif isinstance(error, (ValueError, TypeError)):
            # 保持原有的参数错误提示格式
            QMessageBox.critical(parent_widget, "参数错误", f"{operation}失败:\n{error_message}")
        elif isinstance(error, (OSError, IOError)):
            # 保持原有的系统错误提示格式
            QMessageBox.critical(parent_widget, "系统错误", f"{operation}失败:\n{error_message}")
        else:
            # 未知错误类型 - 保持原有格式
            QMessageBox.critical(parent_widget, "未知错误", f"{operation}失败:\n{error_message}")
            logging.exception(f"未处理的异常类型: {error_type}")
    
    @staticmethod
    def handle_legacy_error(parent_widget, message: str, title: str = "警告", 
                          message_type: str = "warning") -> None:
        """
        处理传统错误消息格式 - 保持向后兼容性
        
        Args:
            parent_widget: 父窗口组件
            message: 错误消息
            title: 对话框标题
            message_type: 消息类型 (warning, critical, information)
        """
        logging.error(f"传统错误处理: {title} - {message}")
        
        if message_type == "warning":
            QMessageBox.warning(parent_widget, title, message)
        elif message_type == "critical":
            QMessageBox.critical(parent_widget, title, message)
        elif message_type == "information":
            QMessageBox.information(parent_widget, title, message)
        else:
            QMessageBox.warning(parent_widget, title, message)
    
    @staticmethod
    def log_operation_start(operation: str, details: str = "") -> None:
        """记录操作开始日志"""
        if details:
            logging.info(f"开始{operation}: {details}")
        else:
            logging.info(f"开始{operation}")
    
    @staticmethod
    def log_operation_success(operation: str, details: str = "") -> None:
        """记录操作成功日志"""
        if details:
            logging.info(f"{operation}成功: {details}")
        else:
            logging.info(f"{operation}成功")
    
    @staticmethod
    def log_operation_failure(operation: str, error: Exception, details: str = "") -> None:
        """记录操作失败日志"""
        error_info = f"{type(error).__name__}: {str(error)}"
        if details:
            logging.error(f"{operation}失败 - {details} - {error_info}")
        else:
            logging.error(f"{operation}失败 - {error_info}")
    
    @staticmethod
    def validate_ppt_file_for_preview(file_path: str) -> None:
        """
        专门用于预览功能的PPT文件验证
        
        Args:
            file_path: PPT文件路径
            
        Raises:
            FileValidationError: 文件验证失败
        """
        # 基础验证
        ErrorHandler.validate_file_path(file_path)
        
        # 检查selective文件限制（保持向后兼容的行为）
        file_name = os.path.basename(file_path)
        if "_selective" in file_name.lower():
            raise FileValidationError("selective类型的文件不能通过此方式打开，请使用其他方式查看此文件")
        
        # 检查文件大小（避免过大的文件导致内存问题）
        try:
            file_size = os.path.getsize(file_path)
            # 如果文件大于100MB，给出警告
            if file_size > 100 * 1024 * 1024:
                logging.warning(f"文件较大 ({file_size / 1024 / 1024:.1f}MB)，可能影响预览性能: {file_path}")
        except Exception as e:
            logging.warning(f"无法获取文件大小: {str(e)}")
    
    @staticmethod
    def create_error_context(operation: str, file_path: str = "", 
                           additional_info: str = "") -> dict:
        """
        创建错误上下文信息，用于更好的错误追踪
        
        Args:
            operation: 操作名称
            file_path: 相关文件路径
            additional_info: 额外信息
            
        Returns:
            dict: 错误上下文信息
        """
        context = {
            "operation": operation,
            "timestamp": time.time(),
            "file_path": file_path,
            "additional_info": additional_info
        }
        return context


class PPTModifierWorker(QThread):
    """PPT修改工作线程"""
    
    progress_updated = pyqtSignal(int, int, int)
    modification_completed = pyqtSignal(list, int)
    modification_stopped = pyqtSignal(list, int)
    
    def __init__(self, 
                file_paths: List[str], 
                slide_range: str, 
                api_key_manager: APIKeyManager,
                text_processor: TextProcessor,
                output_path: str):
        """
        初始化PPT修改工作线程
        
        Args:
            file_paths: PPT文件路径列表
            slide_range: 要处理的幻灯片范围
            api_key_manager: API密钥管理器
            text_processor: 文本处理器
            output_path: 输出目录路径
        """
        super().__init__()
        self.file_paths = file_paths
        self.slide_range = slide_range
        self.api_key_manager = api_key_manager
        self.text_processor = text_processor
        self.output_path = output_path
        self.stop_requested = False
        
    def run(self):
        """线程运行方法"""
        # 创建PPT处理器
        ppt_processor = PPTProcessor(
            self.api_key_manager,
            self.text_processor,
            lambda progress, file_idx, total: self.progress_updated.emit(progress, file_idx, total)
        )
        
        try:
            # 处理PPT文件，每个文件只生成一个修改版本
            modified_file_paths = ppt_processor.process_presentations(
                self.file_paths, self.slide_range, 1, 
                self.output_path
            )
            
            # 发送完成信号
            self.modification_completed.emit(modified_file_paths, len(self.file_paths))
            
        except Exception as e:
            logging.exception(f"PPT修改失败: {str(e)}")
            
            # 发送停止信号
            self.modification_stopped.emit([], 0)
            
    def request_stop(self):
        """请求停止处理"""
        self.stop_requested = True


class OfflinePreviewWorker(QThread):
    """PPT离线预览处理工作线程"""
    
    progress_updated = pyqtSignal(int, int, int)
    preview_completed = pyqtSignal(list, list)  # 返回(result_files, preview_files)
    preview_stopped = pyqtSignal()
    
    def __init__(self, 
                file_paths: List[str], 
                slide_range: str, 
                api_key_manager: APIKeyManager,
                text_processor: TextProcessor,
                output_path: str):
        """
        初始化PPT离线预览处理工作线程
        
        Args:
            file_paths: PPT文件路径列表
            slide_range: 要处理的幻灯片范围
            api_key_manager: API密钥管理器
            text_processor: 文本处理器
            output_path: 输出目录路径
        """
        super().__init__()
        self.file_paths = file_paths
        self.slide_range = slide_range
        self.api_key_manager = api_key_manager
        self.text_processor = text_processor
        self.output_path = output_path
        self.stop_requested = False
        
    def run(self):
        """线程运行方法"""
        # 创建PPT处理器
        ppt_processor = PPTProcessor(
            self.api_key_manager,
            self.text_processor,
            lambda progress, file_idx, total: self.progress_updated.emit(progress, file_idx, len(self.file_paths))
        )
        
        try:
            # 处理文件
            result_files = []
            preview_files = []
            skipped_files = []  # 新增：记录被跳过的文件
            
            for i, file_path in enumerate(self.file_paths):
                if self.stop_requested:
                    break
                    
                # 更新当前正在处理的文件索引
                current_file_index = i + 1
                self.progress_updated.emit(0, current_file_index, len(self.file_paths))
                
                # 记录当前处理的文件名
                file_name = os.path.basename(file_path)
                logging.info(f"正在处理第 {current_file_index}/{len(self.file_paths)} 个文件: {file_name}")
                
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    logging.error(f"文件不存在，跳过处理: {file_path}")
                    self.progress_updated.emit(100, current_file_index, len(self.file_paths))
                    continue
                
                # 处理单个文件
                json_file, preview_file = ppt_processor.process_for_offline_preview(
                    file_path, self.slide_range, self.output_path
                )
                
                # 判断是否为新生成的文件还是已存在的文件
                is_skipped = False
                if preview_file:
                    # 检查文件是否为现有文件
                    file_name = os.path.basename(preview_file)
                    if "_" in file_name:
                        # 如果文件名包含下划线且为preview_开头，可能是新生成的带时间戳的文件
                        name_parts = file_name.split("_")
                        if len(name_parts) > 2 and name_parts[0] == "preview" and re.search(r"\d{8}_\d{6}", file_name):
                            # 这是新生成的文件
                            is_skipped = False
                        else:
                            # 这可能是已有文件
                            is_skipped = True
                    else:
                        # 不含时间戳，可能是已有文件
                        is_skipped = True
                    
                    if is_skipped:
                        skipped_files.append(file_path)
                        logging.info(f"文件已存在，已跳过处理: {preview_file}")
                
                # 更新UI进度
                if is_skipped:
                    # 如果跳过处理，直接显示100%进度
                    self.progress_updated.emit(100, current_file_index, len(self.file_paths))
                
                if json_file:
                    result_files.append(json_file)
                if preview_file:
                    preview_files.append(preview_file)
            
            # 发送完成信号
            self.preview_completed.emit(result_files, preview_files)
            
            # 记录处理结果
            if skipped_files:
                logging.info(f"已跳过处理 {len(skipped_files)}/{len(self.file_paths)} 个已存在的文件")
            
        except Exception as e:
            logging.exception(f"PPT离线预览处理失败: {str(e)}")
            # 发送停止信号
            self.preview_stopped.emit()
            
    def request_stop(self):
        """请求停止处理"""
        self.stop_requested = True


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化设置
        self.settings = QSettings("PPTModifier", "Settings")
        self.min_text_length = self.settings.value("min_text_length", 15, int)
        self.api_keys_file = self.settings.value("api_keys_file", "", str)
        self.default_A_folder = self.settings.value("default_A_folder", "", str)
        self.default_C_folder = self.settings.value("default_C_folder", "", str)
        
        # 加载模型配置文件
        self.model_config_file = ModelConfigFile()
        
        # 创建模型配置管理器
        self.model_config_manager = ModelConfigManager(self.settings, self.model_config_file)
        
        # 初始化工作线程
        self.worker_thread = None
        self.preview_window = None
        
        # 设置UI
        self.init_ui()
        
        # 设置日志区域更新定时器
        self.setup_log_monitor()
        
        # 启用拖放
        self.setAcceptDrops(True)
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口
        self.setWindowTitle('PPT文本润色工具')
        self.setGeometry(100, 100, 900, 700)
        self.setWindowIcon(QIcon('icon.png'))
        
        # 设置应用程序样式
        self.setStyleSheet("""
            QWidget {
                font-family: 'Microsoft YaHei', Arial;
                font-size: 9pt;
                background-color: #2B2B2B;
                color: #FFFFFF;
            }
            QLabel {
                padding: 3px;
                color: #FFFFFF;
            }
            QLineEdit, QSpinBox, QTextEdit {
                padding: 5px;
                border: 1px solid #3C3F41;
                border-radius: 3px;
                background-color: #3C3F41;
                color: #FFFFFF;
                min-height: 20px;
                selection-background-color: #2196F3;
            }
            QLineEdit:focus, QSpinBox:focus, QTextEdit:focus {
                border: 1px solid #2196F3;
            }
            QPushButton {
                padding: 8px;
                border: 1px solid #2196F3;
                border-radius: 3px;
                background-color: #2196F3;
                color: white;
                min-width: 80px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
                border: 1px solid #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QProgressBar {
                border: 1px solid #3C3F41;
                border-radius: 3px;
                text-align: center;
                background-color: #3C3F41;
                color: #FFFFFF;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
            }
            QGroupBox {
                margin-top: 15px;
                padding-top: 15px;
                border: 1px solid #3C3F41;
                border-radius: 5px;
                color: #FFFFFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: #2196F3;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 1px solid #3C3F41;
                background-color: #2B2B2B;
            }
            QTabBar::tab {
                background-color: #3C3F41;
                color: #FFFFFF;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 12px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #455A64;
            }
        """)
        
        # 创建主布局和选项卡控件
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        tab_widget = QTabWidget()
        
        # 创建"修改PPT"选项卡
        ppt_tab = QWidget()
        ppt_layout = QVBoxLayout(ppt_tab)
        ppt_layout.setSpacing(15)
        
        # 设置所有标签的固定宽度
        label_width = 150
        
        # API设置组
        api_group = QGroupBox("API设置")
        api_layout = QVBoxLayout(api_group)
        api_layout.setSpacing(10)
        
        api_key_layout = QHBoxLayout()
        api_key_label = QLabel('API Keys 文件:')
        api_key_label.setFixedWidth(label_width)
        self.api_keys_edit = QLineEdit()
        self.api_keys_edit.setText(self.api_keys_file)
        api_key_button = QPushButton('浏览')
        api_key_button.setFixedWidth(80)
        api_key_button.clicked.connect(self.select_api_keys_file)
        api_key_layout.addWidget(api_key_label)
        api_key_layout.addWidget(self.api_keys_edit)
        api_key_layout.addWidget(api_key_button)
        api_layout.addLayout(api_key_layout)
        
        # 添加模型配置按钮
        model_config_layout = QHBoxLayout()
        model_config_label = QLabel('模型配置:')
        model_config_label.setFixedWidth(label_width)
        model_config_button = QPushButton('编辑模型列表')
        model_config_button.setFixedWidth(100)
        model_config_button.clicked.connect(self.edit_model_config)
        model_config_layout.addWidget(model_config_label)
        model_config_layout.addWidget(model_config_button)
        model_config_layout.addStretch()
        api_layout.addLayout(model_config_layout)
        
        # 添加模型提供商选择
        provider_layout = QHBoxLayout()
        provider_label = QLabel('服务提供商:')
        provider_label.setFixedWidth(label_width)
        self.provider_combo = QComboBox()
        
        # 从配置文件加载提供商
        for provider_id, provider in self.model_config_file.get_providers().items():
            self.provider_combo.addItem(provider.get("name", provider_id), provider_id)
        
        self.provider_combo.currentIndexChanged.connect(self.on_provider_changed)
        provider_layout.addWidget(provider_label)
        provider_layout.addWidget(self.provider_combo)
        api_layout.addLayout(provider_layout)
        
        # 添加模型选择
        model_layout = QHBoxLayout()
        model_label = QLabel('AI模型:')
        model_label.setFixedWidth(label_width)
        self.model_combo = QComboBox()
        self.model_combo.currentIndexChanged.connect(self.on_model_changed)  # 添加模型改变事件处理
        model_layout.addWidget(model_label)
        model_layout.addWidget(self.model_combo)
        api_layout.addLayout(model_layout)
        
        # OpenRouter设置区域
        self.openrouter_settings = QWidget()
        openrouter_layout = QVBoxLayout(self.openrouter_settings)
        openrouter_layout.setContentsMargins(0, 0, 0, 0)
        
        # 保存OpenRouter API Key变量但不显示在UI上
        self.openrouter_api_edit = QLineEdit()
        self.openrouter_api_edit.hide()
        self.openrouter_api_edit.setText(self.settings.value("openrouter_api_key", "", str))
        
        # 保存OpenRouter站点URL变量但不显示在UI上
        self.openrouter_url_edit = QLineEdit()
        self.openrouter_url_edit.hide()
        self.openrouter_url_edit.setText(self.settings.value("openrouter_site_url", "", str))
        
        # 保存OpenRouter站点名称变量但不显示在UI上
        self.openrouter_name_edit = QLineEdit()
        self.openrouter_name_edit.hide()
        self.openrouter_name_edit.setText(self.settings.value("openrouter_site_name", "PPT文本润色工具", str))
        
        api_layout.addWidget(self.openrouter_settings)
        
        # 初始化选定的提供商
        current_provider = self.settings.value("model_provider", "yi", str)
        # 找到对应的索引
        for i in range(self.provider_combo.count()):
            if self.provider_combo.itemData(i) == current_provider:
                self.provider_combo.setCurrentIndex(i)
                break
        
        prompt_layout = QHBoxLayout()
        prompt_label = QLabel('提示词:')
        prompt_label.setFixedWidth(label_width)
        self.prompt_edit = QLineEdit()
        self.prompt_edit.setText(self.settings.value("prompt", 
            "请帮我润色以下文本,请保持一致的语气、风格、句式,使其更加流畅和自然,回答中不要出现多余的内容,请用简体中文回答"))
        prompt_layout.addWidget(prompt_label)
        prompt_layout.addWidget(self.prompt_edit)
        api_layout.addLayout(prompt_layout)
        
        min_length_layout = QHBoxLayout()
        min_length_label = QLabel('最小文本长度:')
        min_length_label.setFixedWidth(label_width)
        self.min_length_edit = QSpinBox()
        self.min_length_edit.setRange(5, 100)
        self.min_length_edit.setValue(self.min_text_length)
        min_length_layout.addWidget(min_length_label)
        min_length_layout.addWidget(self.min_length_edit)
        api_layout.addLayout(min_length_layout)
        
        # 添加清除缓存按钮
        cache_layout = QHBoxLayout()
        cache_label = QLabel('API缓存:')
        cache_label.setFixedWidth(label_width)
        clear_cache_button = QPushButton('清除缓存')
        clear_cache_button.setFixedWidth(100)
        clear_cache_button.clicked.connect(self.clear_api_cache)
        view_cache_button = QPushButton('查看缓存')
        view_cache_button.setFixedWidth(100)
        view_cache_button.clicked.connect(self.view_api_cache)
        cache_layout.addWidget(cache_label)
        cache_layout.addWidget(clear_cache_button)
        cache_layout.addWidget(view_cache_button)
        cache_layout.addStretch()  # 添加弹性空间
        api_layout.addLayout(cache_layout)
        
        ppt_layout.addWidget(api_group)
        
        # 创建文件选择部分
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(10)
        
        # 创建拖放区域
        drop_area_layout = QVBoxLayout()
        self.drop_area = QWidget()
        self.drop_area.setAcceptDrops(False)  # 主窗口已设置接受拖放
        self.drop_area.setMinimumHeight(80)
        self.drop_area.setStyleSheet("""
            background-color: #3C3F41;
            border: 2px dashed #2196F3;
            border-radius: 5px;
        """)
        
        drop_label = QLabel("拖放已修改的PPT文件到此处以启动预览修改")
        drop_label.setAlignment(Qt.AlignCenter)
        drop_label.setStyleSheet("color: #2196F3; font-weight: bold; border: none;")
        
        drop_area_layout.addWidget(drop_label)
        drop_area_layout.setAlignment(Qt.AlignCenter)
        self.drop_area.setLayout(drop_area_layout)
        
        # 添加拖放区域到文件选择区域
        file_layout.addWidget(self.drop_area)
        
        # 原有的文件选择控件
        ppt_file_layout = QHBoxLayout()
        ppt_file_label = QLabel('选择PPT文件:')
        ppt_file_label.setFixedWidth(label_width)
        self.ppt_file_edit = QLineEdit()
        ppt_file_button = QPushButton('浏览')
        ppt_file_button.clicked.connect(self.select_ppt_file)
        auto_select_button = QPushButton('自动加载')
        auto_select_button.clicked.connect(self.auto_select_ppt)
        ppt_file_button.setFixedWidth(80)
        auto_select_button.setFixedWidth(80)
        ppt_file_layout.addWidget(ppt_file_label)
        ppt_file_layout.addWidget(self.ppt_file_edit)
        ppt_file_layout.addWidget(ppt_file_button)
        ppt_file_layout.addWidget(auto_select_button)
        file_layout.addLayout(ppt_file_layout)
        
        folder_layout = QHBoxLayout()
        folder_label = QLabel('选择PPT文件夹:')
        folder_label.setFixedWidth(label_width)
        self.folder_path_edit = QLineEdit()
        folder_button = QPushButton('浏览')
        folder_button.setFixedWidth(80)
        folder_button.clicked.connect(self.select_folder)
        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(folder_button)
        file_layout.addLayout(folder_layout)
        
        output_layout = QHBoxLayout()
        output_label = QLabel('输出文件夹:')
        output_label.setFixedWidth(label_width)
        self.output_path_edit = QLineEdit()
        output_button = QPushButton('浏览')
        output_button.setFixedWidth(80)
        output_button.clicked.connect(self.select_output_folder)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_path_edit)
        output_layout.addWidget(output_button)
        file_layout.addLayout(output_layout)
        
        # 添加随机预览未审阅文件按钮
        random_preview_layout = QHBoxLayout()
        random_preview_btn = QPushButton('随机预览一个未审阅文件')
        random_preview_btn.setToolTip("从输出文件夹中随机选择一个未被审阅的preview文件进行预览")
        random_preview_btn.clicked.connect(self.random_preview_unreviewed_file)
        random_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                min-width: 180px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        random_preview_layout.addStretch()
        random_preview_layout.addWidget(random_preview_btn)
        random_preview_layout.addStretch()
        file_layout.addLayout(random_preview_layout)
        
        ppt_layout.addWidget(file_group)
        
        # 任务设置组
        task_group = QGroupBox("任务设置")
        task_layout = QVBoxLayout(task_group)
        task_layout.setSpacing(10)
        
        slide_layout = QHBoxLayout()
        slide_label = QLabel('幻灯片范围:')
        slide_label.setFixedWidth(label_width)
        self.slide_range_edit = QLineEdit()
        self.slide_range_edit.setPlaceholderText("例如: 2-10 12 15-20 (留空表示所有幻灯片)")
        slide_layout.addWidget(slide_label)
        slide_layout.addWidget(self.slide_range_edit)
        task_layout.addLayout(slide_layout)
        
        ppt_layout.addWidget(task_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 增加预览修改按钮
        preview_button = QPushButton('预览修改')
        preview_button.setFixedWidth(120)
        preview_button.clicked.connect(self.preview_ppt)
        button_layout.addWidget(preview_button)
        
        # 在"预览修改"按钮之后添加两个新按钮
        self.process_offline_button = QPushButton("处理并保存")
        self.process_offline_button.setToolTip("处理PPT并保存结果，用于后续离线预览")
        self.process_offline_button.setIcon(QIcon.fromTheme("document-save"))
        self.process_offline_button.clicked.connect(self.process_for_offline_preview)
        button_layout.addWidget(self.process_offline_button)
        
        self.load_preview_button = QPushButton("加载预览")
        self.load_preview_button.setToolTip("加载之前保存的处理结果进行预览")
        self.load_preview_button.setIcon(QIcon.fromTheme("document-open"))
        self.load_preview_button.clicked.connect(self.load_offline_preview)
        button_layout.addWidget(self.load_preview_button)
        
        self.stop_button = QPushButton('停止')
        self.stop_button.setFixedWidth(120)
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_modification)
        
        button_layout.addWidget(self.stop_button)
        button_layout.addStretch()
        ppt_layout.addLayout(button_layout)
        
        # 进度显示
        progress_group = QGroupBox("处理进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_label = QLabel()
        self.progress_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(20)
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        ppt_layout.addWidget(progress_group)
        
        # 日志选项卡
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        log_buttons_layout = QHBoxLayout()
        clear_log_button = QPushButton("清除日志")
        clear_log_button.clicked.connect(self.clear_log)
        log_buttons_layout.addStretch()
        log_buttons_layout.addWidget(clear_log_button)
        
        log_layout.addLayout(log_buttons_layout)
        
        # 添加选项卡
        tab_widget.addTab(ppt_tab, "修改PPT")
        tab_widget.addTab(log_tab, "日志")
        
        main_layout.addWidget(tab_widget)
        
        # 设置中央窗口
        self.setCentralWidget(central_widget)
    
    def setup_log_monitor(self):
        """设置日志监控"""
        # 创建自定义日志处理器，将日志写入文本控件
        class QTextEditLogger(logging.Handler):
            def __init__(self, text_edit):
                super().__init__()
                self.text_edit = text_edit
                
            def emit(self, record):
                msg = self.format(record)
                self.text_edit.append(msg)
                self.text_edit.moveCursor(QTextCursor.End)
        
        # 配置日志处理器
        text_handler = QTextEditLogger(self.log_text)
        text_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        text_handler.setLevel(logging.INFO)
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(text_handler)
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
    
    def select_api_keys_file(self):
        """选择API密钥文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择API密钥文件', '', 'Text Files (*.txt)')
        if file_path:
            self.api_keys_edit.setText(file_path)
    
    def select_ppt_file(self):
        """选择PPT文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, '选择PPT文件', '', 'PowerPoint Files (*.ppt *.pptx)')
        if file_path:
            self.ppt_file_edit.setText(file_path)
    
    def select_folder(self):
        """选择PPT文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, '选择PPT文件夹', '')
        if folder_path:
            self.folder_path_edit.setText(folder_path)
            
            # 自动检测相关联的输出文件夹
            input_folder, output_folder = self.detect_related_folders(folder_path)
            
            # 如果检测到关联的输出文件夹且输出文件夹编辑框为空，则自动填充
            if output_folder and not self.output_path_edit.text():
                self.output_path_edit.setText(output_folder)
                logging.info(f"已自动设置输出文件夹: {output_folder}")
    
    def select_output_folder(self):
        """选择输出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, '选择输出文件夹', '')
        if folder_path:
            self.output_path_edit.setText(folder_path)
            
            # 自动检测相关联的输入文件夹
            input_folder, output_folder = self.detect_related_folders(folder_path)
            
            # 如果检测到关联的输入文件夹且输入文件夹编辑框为空，则自动填充
            if input_folder and not self.folder_path_edit.text():
                self.folder_path_edit.setText(input_folder)
                logging.info(f"已自动设置输入文件夹: {input_folder}")
    
    def auto_select_ppt(self):
        """改进的自动加载PPT文件方法"""
        try:
            logging.info("开始自动加载PPT文件...")
            
            # 1. 判断默认查找文件夹是否存在
            if not os.path.exists(self.default_A_folder):
                QMessageBox.warning(self, '警告', f'默认查找文件夹不存在：{self.default_A_folder}')
                return
                
            # 2. 查找所有子文件夹（按修改时间排序）
            subfolders = [os.path.join(self.default_A_folder, d) for d in os.listdir(self.default_A_folder) 
                         if os.path.isdir(os.path.join(self.default_A_folder, d))]
            
            if not subfolders:
                QMessageBox.warning(self, '警告', f'默认查找文件夹内没有子文件夹：{self.default_A_folder}')
                return
                
            # 按修改时间排序，最新的在前
            latest_subfolders = sorted(subfolders, key=os.path.getmtime, reverse=True)
            logging.info(f"找到 {len(latest_subfolders)} 个子文件夹，按修改时间排序")
            
            # 3. 在子文件夹中查找最新的PPT文件
            latest_ppt = None
            for subfolder in latest_subfolders:
                ppt_files = [os.path.join(subfolder, f) for f in os.listdir(subfolder) 
                            if f.lower().endswith(('.ppt', '.pptx'))]
                if ppt_files:
                    latest_ppt = max(ppt_files, key=os.path.getmtime)
                    logging.info(f"在子文件夹 {os.path.basename(subfolder)} 中找到最新PPT文件：{os.path.basename(latest_ppt)}")
                    break
                    
            if not latest_ppt:
                QMessageBox.warning(self, '警告', '在子文件夹中未找到PPT文件')
                return
                
            # 4. 智能识别当前应使用的输入和输出文件夹
            base_dir = r"E:\同步\百度文库\改写"
            if not os.path.exists(base_dir):
                QMessageBox.warning(self, '警告', f'工作目录不存在：{base_dir}')
                return
                
            # 查找所有形如folder_XX的文件夹
            input_folders = [d for d in os.listdir(base_dir) 
                            if os.path.isdir(os.path.join(base_dir, d)) 
                            and d.startswith("folder_") and d[7:].isdigit()]
                            
            if not input_folders:
                QMessageBox.warning(self, '警告', '未找到输入文件夹')
                return
                
            # 按数字部分排序，找出最新的文件夹
            sorted_inputs = sorted(input_folders, 
                                 key=lambda d: int(d[7:]) if d[7:].isdigit() else 0, 
                                 reverse=True)
            latest_input_folder_name = sorted_inputs[0]
            folder_num = latest_input_folder_name[7:]  # 获取XX部分
            
            latest_input = os.path.join(base_dir, latest_input_folder_name)
            
            # 查找对应的输出文件夹
            output_pattern = os.path.join(base_dir, f"{folder_num}-*")
            potential_outputs = glob.glob(output_pattern)
            
            # 如果没有找到对应的输出文件夹，则创建一个
            if not potential_outputs:
                new_output_folder = os.path.join(base_dir, f"{folder_num}-1")
                os.makedirs(new_output_folder, exist_ok=True)
                latest_output = new_output_folder
                logging.info(f"创建新的输出文件夹：{new_output_folder}")
            else:
                # 按后缀数字排序，找出最新的输出文件夹
                sorted_outputs = sorted(potential_outputs, 
                                      key=lambda p: int(os.path.basename(p).split('-')[1]) 
                                      if len(os.path.basename(p).split('-')) > 1 and 
                                      os.path.basename(p).split('-')[1].isdigit() else 0,
                                      reverse=True)
                latest_output = sorted_outputs[0]
            
            # 5. 复制文件到输入文件夹（正确的目标是输入文件夹而不是输出文件夹）
            destination_file = os.path.join(latest_input, os.path.basename(latest_ppt))
            
            # 检查目标文件是否已存在
            if os.path.exists(destination_file):
                reply = QMessageBox.question(
                    self, '文件已存在', 
                    f'文件 {os.path.basename(destination_file)} 已存在，是否覆盖？',
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.No:
                    # 不覆盖，但仍然设置路径（不设置文件夹路径）
                    self.ppt_file_edit.setText(destination_file)
                    self.output_path_edit.setText(latest_output)
                    logging.info(f"文件已存在，未覆盖：{destination_file}")
                    return
                    
            # 复制文件
            try:
                copy_file(latest_ppt, destination_file)
                self.ppt_file_edit.setText(destination_file)
                self.output_path_edit.setText(latest_output)
                
                QMessageBox.information(
                    self, '自动加载成功', 
                    f'已将文件 {os.path.basename(latest_ppt)} 复制到输入文件夹 {os.path.basename(latest_input)}'
                )
                logging.info(f"成功复制文件：{latest_ppt} -> {destination_file}")
                
            except Exception as e:
                QMessageBox.warning(self, '复制失败', f'复制文件失败: {str(e)}')
                logging.error(f"复制文件失败: {str(e)}")
                
        except Exception as e:
            logging.error(f"自动加载PPT文件失败: {str(e)}")
            QMessageBox.warning(self, '错误', f'自动加载PPT文件失败: {str(e)}')
    
    def get_selected_files(self):
        """获取用户选择的文件列表 - 使用标准化错误处理"""
        file_path = self.ppt_file_edit.text()
        folder_path = self.folder_path_edit.text()
        
        try:
            # 获取要处理的文件列表
            if folder_path and os.path.exists(folder_path):
                # 验证文件夹
                ErrorHandler.validate_folder_path(folder_path)
                
                file_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if
                             f.lower().endswith(('.ppt', '.pptx'))]
                if not file_paths:
                    # 保持向后兼容的错误提示
                    ErrorHandler.handle_legacy_error(self, '所选文件夹中没有发现PPT文件', '警告', 'warning')
                    return []
                return file_paths
            elif file_path and os.path.exists(file_path):
                # 验证单个文件
                ErrorHandler.validate_file_path(file_path)
                return [file_path]
            else:
                return []
                
        except FileValidationError as e:
            ErrorHandler.handle_error(self, e, "获取文件列表")
            return []
        except Exception as e:
            ErrorHandler.handle_error(self, e, "获取文件列表")
            return []
    
    def modify_ppt(self):
        """开始处理PPT文件"""
        # 获取输入值
        file_path = self.ppt_file_edit.text()
        folder_path = self.folder_path_edit.text()
        output_path = self.output_path_edit.text()
        slide_range = self.slide_range_edit.text()
        num_modified_files = self.num_files_edit.value()
        api_keys_file = self.api_keys_edit.text()
        prompt = self.prompt_edit.text()
        min_text_length = self.min_length_edit.value()
        
        # 验证输入 - 使用标准化错误处理保持向后兼容性
        try:
            if not folder_path and not file_path:
                raise FileValidationError('请选择PPT文件或文件夹')
            
            if not output_path:
                raise FileValidationError('请选择输出文件夹')
            
            if not os.path.exists(api_keys_file):
                raise FileValidationError(f'API密钥文件不存在: {api_keys_file}')
            
            if num_modified_files <= 0:
                raise ValueError('修改版本数量必须大于0')
                
        except (FileValidationError, ValueError) as e:
            ErrorHandler.handle_legacy_error(self, str(e), '警告', 'warning')
            return
        
        # 获取要处理的文件列表
        file_paths = self.get_selected_files()
        if not file_paths:
            return
        
        # 保存设置
        self.settings.setValue("min_text_length", min_text_length)
        self.settings.setValue("api_keys_file", api_keys_file)
        self.settings.setValue("prompt", prompt)
        
        # 初始化API密钥管理器和文本处理器
        api_key_manager = APIKeyManager(api_keys_file)
        text_processor = TextProcessor(api_key_manager, prompt, self.model_config_manager, min_text_length)
        
        # 准备进度显示
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("开始处理PPT文件...")
        self.stop_button.setEnabled(True)
        
        # 创建工作线程处理PPT文件
        self.worker_thread = PPTModifierWorker(
            file_paths, 
            slide_range, 
            api_key_manager,
            text_processor,
            output_path
        )
        
        # 连接信号
        self.worker_thread.progress_updated.connect(self.update_progress)
        self.worker_thread.modification_completed.connect(self.handle_modification_completed)
        self.worker_thread.modification_stopped.connect(self.handle_modification_stopped)
    
    def stop_modification(self):
        """停止修改过程"""
        if hasattr(self, 'worker_thread') and self.worker_thread and self.worker_thread.isRunning():
            logging.info("请求停止PPT修改")
            self.worker_thread.request_stop()
            self.progress_label.setText("正在停止处理...")
            
        if hasattr(self, 'offline_preview_worker') and self.offline_preview_worker and self.offline_preview_worker.isRunning():
            logging.info("请求停止离线预览处理")
            self.offline_preview_worker.request_stop()
            self.progress_label.setText("正在停止处理...")
            
        self.processing_canceled = True
    
    def update_progress(self, progress, file_index, total_files):
        """更新进度显示"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(f"正在修改第 {file_index}/{total_files} 个PPT, 进度: {progress}%")
    
    def handle_modification_completed(self, modified_file_paths, total_files):
        """处理修改完成信号"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"修改完成！成功处理 {len(modified_file_paths)} 个文件。")
        self.stop_button.setEnabled(False)
        
        # 记录日志
        logging.info(f"修改任务完成: 成功处理 {len(modified_file_paths)} 个文件")
        
        # 弹窗询问是否打开修改后的PPT文件
        open_files = QMessageBox.question(self, '修改完毕', 
                                         '修改完成，是否打开修改后的PPT文件?',
                                         QMessageBox.Open | QMessageBox.Cancel)
        
        if open_files == QMessageBox.Open:
            # 先打开原始文件
            original_file = self.ppt_file_edit.text()
            if original_file:
                open_file(original_file)
            
            # 然后打开修改后的文件
            for modified_file_path in modified_file_paths:
                open_file(modified_file_path)
    
    def handle_modification_stopped(self, modified_file_paths, current_file_index):
        """处理修改停止信号"""
        self.progress_bar.setVisible(False)
        self.progress_label.setText(f"修改已停止！已处理 {len(modified_file_paths)} 个文件。")
        self.stop_button.setEnabled(False)
        
        # 记录日志
        logging.info(f"修改任务已停止: 已处理 {len(modified_file_paths)} 个文件")
        
        # 弹窗询问是否打开修改后的PPT文件
        open_files = QMessageBox.question(self, '修改停止', 
                                         '修改已停止，是否打开已修改的PPT文件?',
                                         QMessageBox.Open | QMessageBox.Cancel)
        
        if open_files == QMessageBox.Open:
            # 先打开原始文件
            original_file = self.ppt_file_edit.text()
            if original_file:
                open_file(original_file)
            
            # 然后打开修改后的文件
            for modified_file_path in modified_file_paths:
                open_file(modified_file_path)
    
    def preview_ppt(self):
        """
        预览修改按钮处理函数 - 重构后统一使用preview_file()
        
        重构说明：
        - 移除了原有的PPT处理逻辑和临时文件生成
        - 简化为文件选择和预览调用
        - 统一使用preview_file()函数进行预览
        - 保持原有的文件验证和错误提示机制
        
        处理流程：
        1. 获取用户选择的文件列表
        2. 验证文件选择（至少选择一个文件）
        3. 处理多文件选择情况（使用第一个文件）
        4. 调用统一的preview_file()函数
        
        错误处理：
        - FileValidationError: 文件选择或验证错误
        - 其他异常: 统一错误处理和用户提示
        
        需求对应: 2.1, 2.2, 2.3, 2.4
        """
        operation = "预览修改"
        ErrorHandler.log_operation_start(operation)
        
        try:
            # 获取用户选择的文件
            file_paths = self.get_selected_files()
            if not file_paths:
                raise FileValidationError("请选择至少一个PPT文件")
            
            # 处理多文件选择情况（使用第一个文件）
            file_path = file_paths[0]
            if len(file_paths) > 1:
                logging.info(f"选择了多个文件，将预览第一个文件: {os.path.basename(file_path)}")
            
            # 标准化文件验证
            ErrorHandler.validate_file_path(file_path)
            
            # 直接调用统一的预览函数
            self.preview_file(file_path)
            
            ErrorHandler.log_operation_success(operation, os.path.basename(file_path))
            
        except FileValidationError as e:
            ErrorHandler.handle_error(self, e, operation)
        except Exception as e:
            ErrorHandler.log_operation_failure(operation, e)
            ErrorHandler.handle_error(self, e, operation)
    
    def handle_preview_completed(self, modified_file_paths):
        """处理预览完成后的操作"""
        if modified_file_paths:
            # 询问是否打开修改后的文件
            reply = QMessageBox.question(
                self, 
                "预览完成", 
                "预览修改已完成并保存选择的修改，是否打开修改后的文件？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 查找预览窗口中的原始文件路径
                original_file = None
                if hasattr(self, 'preview_window') and self.preview_window:
                    if hasattr(self.preview_window, 'original_ppt_path') and self.preview_window.original_ppt_path:
                        original_file = self.preview_window.original_ppt_path
                
                # 如果无法从预览窗口获取原始文件，尝试从文件输入框获取
                if not original_file:
                    original_file = self.ppt_file_edit.text()
                
                # 先打开原始文件
                if original_file and os.path.exists(original_file):
                    logging.info(f"打开原始文件: {original_file}")
                    open_file(original_file)
                
                # 然后打开修改后的文件
                for path in modified_file_paths:
                    logging.info(f"打开修改后文件: {path}")
                    open_file(path)
            
            # 清空三个输入框，以便于下次预览其他文件
            self.ppt_file_edit.clear()
            self.folder_path_edit.clear()
            self.output_path_edit.clear()
            logging.info("预览完成，已清空文件路径输入框以便于下次预览")
    
    def closeEvent(self, event):
        """窗口关闭事件，保存设置"""
        # 保存设置
        self.settings.setValue("min_text_length", self.min_length_edit.value())
        self.settings.setValue("api_keys_file", self.api_keys_edit.text())
        self.settings.setValue("prompt", self.prompt_edit.text())
        
        # 保存模型设置
        provider = self.provider_combo.itemData(self.provider_combo.currentIndex())
        self.settings.setValue("model_provider", provider)
        
        if provider == "openrouter":
            self.settings.setValue("openrouter_api_key", self.openrouter_api_edit.text())
            self.settings.setValue("openrouter_site_url", self.openrouter_url_edit.text())
            self.settings.setValue("openrouter_site_name", self.openrouter_name_edit.text())
        
        model_id = self.model_combo.itemData(self.model_combo.currentIndex()) if self.model_combo.count() > 0 else ""
        self.settings.setValue("model_id", model_id)
        
        # 如果工作线程正在运行，请求停止
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.request_stop()
            self.worker_thread.wait(1000)  # 等待最多1秒钟
        
        event.accept()

    # 添加拖放事件处理方法
    def dragEnterEvent(self, event):
        """处理拖放进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.drop_area.setStyleSheet("""
                background-color: #2C3E50;
                border: 2px dashed #3498DB;
                border-radius: 5px;
            """)
    
    def dragLeaveEvent(self, event):
        """处理拖放离开事件"""
        self.drop_area.setStyleSheet("""
            background-color: #3C3F41;
            border: 2px dashed #2196F3;
            border-radius: 5px;
        """)
        event.accept()
    
    def dropEvent(self, event):
        """
        拖放事件处理函数 - 重构后统一使用preview_file()
        
        重构说明：
        - 移除了对preview文件的特殊判断逻辑
        - 移除了用户确认对话框
        - 统一调用preview_file()函数进行预览
        - 保持原有的文件路径设置和文件夹自动检测功能
        
        处理流程：
        1. 验证拖放内容是否包含有效文件路径
        2. 获取拖放的文件路径
        3. 根据文件类型进行不同处理：
           - PPT文件：设置文件路径 → 自动检测文件夹 → 调用preview_file()
           - 文件夹：检测相关文件夹并设置输入/输出路径
        4. 重置拖放区域样式
        
        错误处理：
        - FileValidationError: 文件验证或路径错误
        - 其他异常: 统一错误处理和用户提示
        
        需求对应: 1.1, 1.3, 1.4, 1.5
        """
        operation = "拖放文件"
        
        # 重置拖放区域样式
        self.drop_area.setStyleSheet("""
            background-color: #3C3F41;
            border: 2px dashed #2196F3;
            border-radius: 5px;
        """)
        
        try:
            if not event.mimeData().hasUrls():
                raise FileValidationError("拖放的内容不包含有效的文件路径")
            
            url = event.mimeData().urls()[0]
            file_path = url.toLocalFile()
            
            if not file_path:
                raise FileValidationError("无法获取拖放文件的路径")
            
            ErrorHandler.log_operation_start(operation, file_path)
            
            if file_path.lower().endswith(('.ppt', '.pptx')):
                # 处理PPT文件拖放
                file_name = os.path.basename(file_path)
                
                # 设置PPT文件路径
                self.ppt_file_edit.setText(file_path)
                
                # 自动检测文件夹（保持原有功能）
                self.auto_detect_folders_from_file(file_path)
                
                # 统一调用preview_file()函数（包含所有必要的验证）
                self.preview_file(file_path)
                
                ErrorHandler.log_operation_success(operation, f"PPT文件: {file_name}")
                
            elif os.path.isdir(file_path):
                # 处理文件夹拖放
                ErrorHandler.validate_folder_path(file_path)
                
                logging.info(f"收到拖放文件夹: {file_path}")
                input_folder, output_folder = self.detect_related_folders(file_path)
                
                # 根据检测结果设置相应的文件夹
                if input_folder:
                    self.folder_path_edit.setText(input_folder)
                    logging.info(f"已设置输入文件夹: {input_folder}")
                
                if output_folder:
                    self.output_path_edit.setText(output_folder)
                    logging.info(f"已设置输出文件夹: {output_folder}")
                
                # 如果拖入的就是检测到的输入或输出文件夹，直接设置
                if file_path == input_folder:
                    self.folder_path_edit.setText(file_path)
                elif file_path == output_folder:
                    self.output_path_edit.setText(file_path)
                # 如果没有检测到任何关联文件夹，尝试判断拖入文件夹的类型
                elif not input_folder and not output_folder:
                    folder_name = os.path.basename(file_path)
                    if folder_name.startswith("folder_"):
                        self.folder_path_edit.setText(file_path)
                        logging.info(f"将拖入文件夹设为输入文件夹: {file_path}")
                    elif "-" in folder_name and len(folder_name.split("-")) == 2:
                        self.output_path_edit.setText(file_path)
                        logging.info(f"将拖入文件夹设为输出文件夹: {file_path}")
                
                ErrorHandler.log_operation_success(operation, f"文件夹: {os.path.basename(file_path)}")
                
            else:
                raise FileValidationError("请拖放PPT、PPTX文件或文件夹")
        
        except FileValidationError as e:
            ErrorHandler.handle_error(self, e, operation)
        except Exception as e:
            ErrorHandler.log_operation_failure(operation, e)
            ErrorHandler.handle_error(self, e, operation)
        
        event.acceptProposedAction()
    
    def auto_detect_folders_from_file(self, file_path):
        """从拖放的文件路径推断文件夹命名规则"""
        try:
            # 提取文件所在目录
            file_dir = os.path.dirname(file_path)
            dir_name = os.path.basename(file_dir)
            
            # 1. 首先检查文件是否在输出目录(XX-Y 格式)中
            if '-' in dir_name:
                parts = dir_name.split('-')
                if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                    folder_num = parts[0]
                    
                    # 构建可能的folder_XX路径
                    parent_dir = os.path.dirname(file_dir)
                    potential_source_dir = os.path.join(parent_dir, f"folder_{folder_num}")
                    
                    # 检查源文件夹是否存在
                    if os.path.exists(potential_source_dir) and os.path.isdir(potential_source_dir):
                        # 更新设置
                        self.folder_path_edit.setText(potential_source_dir)
                        self.output_path_edit.setText(file_dir)
                        logging.info(f"自动检测到源文件夹: {potential_source_dir} 和输出文件夹: {file_dir}")
                        return
            
            # 2. 检查文件是否在输入目录(folder_XX 格式)中
            if dir_name.startswith("folder_") and len(dir_name) > 7:
                folder_num = dir_name[7:]  # 移除"folder_"前缀
                if folder_num.isdigit():
                    # 查找可能的输出目录 (XX-Y 格式)
                    parent_dir = os.path.dirname(file_dir)
                    pattern = os.path.join(parent_dir, f"{folder_num}-*")
                    potential_outputs = glob.glob(pattern)
                    
                    if potential_outputs:
                        # 按照数字顺序找到最新的输出文件夹
                        sorted_outputs = sorted(potential_outputs, 
                                              key=lambda p: int(os.path.basename(p).split('-')[1]) 
                                              if os.path.basename(p).split('-')[1].isdigit() else 0,
                                              reverse=True)
                        output_dir = sorted_outputs[0]
                        
                        # 更新设置
                        self.folder_path_edit.setText(file_dir)
                        self.output_path_edit.setText(output_dir)
                        logging.info(f"自动检测到源文件夹: {file_dir} 和输出文件夹: {output_dir}")
                        return
            
            # 如果上述规则都不匹配，则尝试使用文件所在目录作为默认值
            if not self.folder_path_edit.text():
                self.folder_path_edit.setText(file_dir)
            if not self.output_path_edit.text():
                self.output_path_edit.setText(file_dir)
                
        except Exception as e:
            logging.error(f"自动检测文件夹出错: {str(e)}")
    
    def create_api_key_manager(self):
        """
        创建API密钥管理器 - 使用标准化错误处理机制
        
        Returns:
            APIKeyManager: API密钥管理器实例
        """
        try:
            api_keys_file = self.api_keys_edit.text().strip()
            
            # 标准化API密钥文件验证
            ErrorHandler.validate_api_keys_file(api_keys_file)
            
            # 创建API密钥管理器
            api_key_manager = APIKeyManager(api_keys_file)
            logging.debug(f"API密钥管理器创建成功: {api_keys_file}")
            
            return api_key_manager
            
        except FileValidationError:
            # 重新抛出验证错误，让调用者处理
            raise
        except Exception as e:
            logging.error(f"创建API密钥管理器失败: {str(e)}")
            raise SystemResourceError(f"创建API密钥管理器失败: {str(e)}")
    
    def create_text_processor(self):
        """
        创建文本处理器 - 统一文本处理器创建方式
        
        Returns:
            TextProcessor: 文本处理器实例
            
        Raises:
            RuntimeError: 文本处理器创建失败
        """
        try:
            # 创建API密钥管理器
            api_key_manager = self.create_api_key_manager()
            
            # 获取提示和最小文本长度
            prompt = self.prompt_edit.text().strip()
            min_text_length = self.min_length_edit.value()
            
            # 验证参数
            if not prompt:
                raise ValueError("提示词不能为空")
            
            if min_text_length <= 0:
                raise ValueError("最小文本长度必须大于0")
            
            # 创建文本处理器
            processor = TextProcessor(api_key_manager, prompt, self.model_config_manager, min_text_length)
            
            # 获取当前提供商
            provider = self.provider_combo.itemData(self.provider_combo.currentIndex())
            
            # 对于OpenRouter提供商，记录API Key状态(不显示实际内容，仅长度)
            if provider == "openrouter":
                openrouter_api_key = self.openrouter_api_edit.text()
                key_length = len(openrouter_api_key) if openrouter_api_key else 0
                logging.debug(f"更新OpenRouter设置 - API Key长度: {key_length}")
                
                # 检查API Key是否为空
                if key_length == 0:
                    logging.warning("OpenRouter API Key为空，API调用可能会失败")
            
            # 更新模型配置
            self.model_config_manager.update_provider_settings(
                provider=provider, 
                openrouter_api_key=self.openrouter_api_edit.text() if provider == "openrouter" else "",
                openrouter_site_url=self.openrouter_url_edit.text() if provider == "openrouter" else "",
                openrouter_site_name=self.openrouter_name_edit.text() if provider == "openrouter" else ""
            )
            
            # 设置活动模型 - 获取当前选中的模型ID而不是使用processor.set_active_model
            if self.model_combo.count() > 0:
                model_id = self.model_combo.itemData(self.model_combo.currentIndex())
                self.model_config_manager.set_active_model(provider, model_id)
                logging.debug(f"创建文本处理器: 设置当前模型配置 - 提供商: {provider}, 模型: {model_id}")
            
            logging.debug("文本处理器创建成功")
            return processor
            
        except Exception as e:
            logging.error(f"创建文本处理器时出错: {str(e)}")
            raise RuntimeError(f"创建文本处理器失败: {str(e)}")
    
    def preview_file(self, file_path):
        """
        统一的文件预览接口 - 所有预览功能的核心函数
        
        重构说明：
        - 作为所有预览功能的统一入口点
        - 拖放功能、预览修改按钮、随机预览功能都调用此函数
        - 使用增强的标准化错误处理机制
        - 统一预览窗口创建逻辑和参数
        - 完善的资源管理和内存优化
        
        处理流程：
        1. 专门的预览文件验证（包含selective文件检查）
        2. 统一任务冲突检查
        3. 检查预览窗口冲突
        4. 标准化系统资源检查
        5. 创建API密钥管理器和文本处理器
        6. 清理旧的预览窗口
        7. 创建新的预览窗口（标准化参数）
        8. 设置窗口标题
        9. 加载文件到预览窗口
        10. 显示预览窗口（统一显示方式）
        11. 优化内存使用
        
        错误处理：
        - FileValidationError: 文件验证失败（包括selective文件限制）
        - TaskConflictError: 任务冲突或预览窗口冲突
        - SystemResourceError: 系统资源不足或组件创建失败
        - 其他异常: 统一错误处理和用户提示
        
        Args:
            file_path (str): PPT文件路径
            
        需求对应: 1.1, 2.1, 4.1, 4.2, 4.3, 4.4, 5.3
        """
        operation = "预览文件"
        error_context = ErrorHandler.create_error_context(operation, file_path)
        ErrorHandler.log_operation_start(operation, file_path)
        
        # 增强调试日志记录
        self._enhance_debug_logging(operation, {"file_path": file_path})
        
        try:
            # 1. 专门的预览文件验证（包含selective文件检查）
            ErrorHandler.validate_ppt_file_for_preview(file_path)
            
            # 2. 统一任务冲突检查
            ErrorHandler.check_task_conflict(self.worker_thread)
            
            # 3. 检查预览窗口冲突
            ErrorHandler.check_preview_window_conflict(self.preview_window)
            
            # 4. 标准化系统资源检查
            ErrorHandler.validate_system_resources()
            
            # 5. 创建API密钥管理器和文本处理器
            logging.debug("创建API密钥管理器和文本处理器")
            api_key_manager = self.create_api_key_manager()
            if not api_key_manager:
                raise SystemResourceError("无法创建API密钥管理器")
            
            text_processor = self.create_text_processor()
            if not text_processor:
                raise SystemResourceError("无法创建文本处理器")
            
            # 6. 清理旧的预览窗口
            self._cleanup_preview_window()
            
            # 7. 创建新的预览窗口
            logging.debug("创建预览窗口")
            self.preview_window = self._create_preview_window(api_key_manager, text_processor)
            
            # 8. 设置窗口标题
            self._set_preview_window_title(file_path)
            
            # 9. 加载文件到预览窗口
            file_name = os.path.basename(file_path)
            logging.debug(f"加载文件到预览窗口: {file_name}")
            self.preview_window.load_ppt(file_path)
            
            # 10. 显示预览窗口
            self._show_preview_window()
            
            # 11. 优化内存使用
            self._optimize_memory_usage()
            
            ErrorHandler.log_operation_success(operation, file_name)
            
        except (FileValidationError, TaskConflictError, SystemResourceError) as e:
            ErrorHandler.handle_error(self, e, operation)
        except Exception as e:
            ErrorHandler.log_operation_failure(operation, e, file_path)
            ErrorHandler.handle_error(self, e, operation)
    
    def _cleanup_preview_window(self):
        """
        清理旧的预览窗口 - 标准化旧窗口清理逻辑
        
        重构说明：
        - 统一窗口清理逻辑，确保所有预览功能使用相同的清理方式
        - 标准化窗口关闭流程，避免内存泄漏
        - 断开信号连接，释放相关资源
        - 异常安全处理，确保即使出错也能清空引用
        
        处理流程：
        1. 检查预览窗口是否存在
        2. 隐藏窗口（如果可见）
        3. 断开信号连接
        4. 关闭窗口
        5. 标记为删除
        6. 清空引用
        
        错误处理：
        - 信号断开失败：忽略（可能已断开或对象已删除）
        - 其他异常：记录警告日志，确保清空引用
        
        需求对应: 4.3, 4.4
        """
        if hasattr(self, 'preview_window') and self.preview_window:
            try:
                logging.debug("开始清理旧的预览窗口")
                
                # 标准化窗口关闭流程
                if self.preview_window.isVisible():
                    self.preview_window.hide()
                    logging.debug("隐藏预览窗口")
                
                # 断开信号连接，避免内存泄漏
                try:
                    self.preview_window.preview_completed.disconnect()
                except (TypeError, RuntimeError):
                    # 信号可能已经断开或对象已被删除
                    pass
                
                # 关闭窗口
                self.preview_window.close()
                logging.debug("关闭预览窗口")
                
                # 标记为删除
                self.preview_window.deleteLater()
                
                # 清空引用
                self.preview_window = None
                
                logging.debug("预览窗口清理完成")
                
            except Exception as e:
                logging.warning(f"清理预览窗口时出错: {str(e)}")
                # 即使出错也要清空引用
                self.preview_window = None
    
    def _optimize_memory_usage(self):
        """
        优化内存使用 - 清理不必要的资源和缓存
        
        重构说明：
        - 作为预览功能统一化的一部分，确保内存使用效率
        - 在每次预览操作后调用，避免内存泄漏
        - 使用标准化的内存清理流程
        
        处理流程：
        1. 强制执行垃圾回收，释放未引用的对象
        2. 清理Qt对象缓存，释放GUI相关资源
        3. 处理事件队列，确保UI响应性
        4. 记录内存优化结果
        
        错误处理：
        - 异常安全处理，确保即使出错也不影响主要功能
        - 记录警告日志，便于问题排查
        
        需求对应: 5.5 (代码重构和优化)
        """
        try:
            logging.debug("开始优化内存使用")
            
            # 强制垃圾回收
            import gc
            collected = gc.collect()
            if collected > 0:
                logging.debug(f"垃圾回收释放了 {collected} 个对象")
            
            # 清理Qt对象缓存
            from PyQt5.QtCore import QCoreApplication
            QCoreApplication.processEvents()
            
            logging.debug("内存优化完成")
            
        except Exception as e:
            logging.warning(f"内存优化时出错: {str(e)}")
    
    def _enhance_debug_logging(self, operation: str, details: dict = None):
        """
        增强调试日志记录 - 提供更详细的调试信息
        
        重构说明：
        - 作为预览功能统一化的一部分，提供详细的性能监控
        - 记录内存使用、CPU使用率等系统资源信息
        - 帮助识别性能瓶颈和资源泄漏问题
        - 支持可选的psutil模块，无依赖时降级处理
        
        处理流程：
        1. 尝试获取当前进程的系统资源信息
        2. 记录内存使用情况（RSS和VMS）
        3. 记录CPU使用率和线程数量
        4. 合并用户提供的详细信息
        5. 输出格式化的调试日志
        
        错误处理：
        - ImportError: psutil模块不可用时降级为基本日志
        - 其他异常: 记录调试日志失败信息
        
        Args:
            operation (str): 操作名称
            details (dict, optional): 额外的详细信息字典
            
        需求对应: 5.3 (错误处理说明), 5.5 (代码重构和优化)
        """
        try:
            import psutil
            import os
            
            # 获取当前进程信息
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            debug_info = {
                "operation": operation,
                "memory_rss": f"{memory_info.rss / 1024 / 1024:.1f}MB",
                "memory_vms": f"{memory_info.vms / 1024 / 1024:.1f}MB", 
                "cpu_percent": f"{cpu_percent:.1f}%",
                "thread_count": process.num_threads()
            }
            
            if details:
                debug_info.update(details)
            
            logging.debug(f"性能监控 - {operation}: {debug_info}")
            
        except ImportError:
            # 如果没有psutil，只记录基本信息
            logging.debug(f"调试信息 - {operation}: {details or {}}")
        except Exception as e:
            logging.debug(f"调试日志记录失败: {str(e)}")
    
    def _create_preview_window(self, api_key_manager, text_processor):
        """
        创建预览窗口 - 标准化PreviewWindow初始化
        
        重构说明：
        - 统一API密钥管理器和文本处理器创建方式
        - 统一窗口标志和样式设置
        - 确保所有预览功能使用相同的窗口创建参数
        - 标准化错误处理和异常抛出
        
        处理流程：
        1. 验证传入的管理器和处理器有效性
        2. 创建PreviewWindow实例（使用标准化参数）
        3. 设置统一窗口标志和样式
        4. 返回创建的预览窗口实例
        
        错误处理：
        - ValueError: 管理器或处理器为空
        - RuntimeError: 预览窗口创建失败
        
        Args:
            api_key_manager: API密钥管理器实例
            text_processor: 文本处理器实例
            
        Returns:
            PreviewWindow: 创建的预览窗口实例
            
        Raises:
            RuntimeError: 预览窗口创建失败
            
        需求对应: 4.1, 4.2
        """
        try:
            logging.debug("开始创建预览窗口")
            
            # 统一API密钥管理器和文本处理器创建方式
            # 确保传入的管理器和处理器有效
            if not api_key_manager:
                raise ValueError("API密钥管理器不能为空")
            if not text_processor:
                raise ValueError("文本处理器不能为空")
            
            # 创建新的预览窗口 - 使用标准化参数
            preview_window = PreviewWindow(
                original_ppt_path=None,
                modified_ppt_path=None,
                text_changes=None,
                slides_to_modify=None,
                api_key_manager=api_key_manager, 
                text_processor=text_processor
            )
            
            # 统一窗口标志和样式设置
            preview_window.setWindowFlags(Qt.Window)
            preview_window.setStyleSheet(self.get_preview_style())
            
            logging.debug("预览窗口创建成功")
            return preview_window
            
        except Exception as e:
            logging.error(f"创建预览窗口失败: {str(e)}")
            raise RuntimeError(f"创建预览窗口失败: {str(e)}")
    
    def _show_preview_window(self):
        """
        显示预览窗口 - 统一窗口显示和激活方式
        
        确保预览窗口以标准化的方式显示和激活
        
        Raises:
            RuntimeError: 显示预览窗口失败
        """
        try:
            if not self.preview_window:
                raise ValueError("预览窗口不存在")
            
            logging.debug("开始显示预览窗口")
            
            # 统一窗口显示方式
            self.preview_window.show()
            
            # 统一窗口激活方式
            self.preview_window.raise_()  # 确保窗口在最前面
            self.preview_window.activateWindow()  # 激活窗口
            
            # 确保窗口获得焦点
            self.preview_window.setFocus()
            
            logging.debug("预览窗口显示和激活成功")
            
        except Exception as e:
            logging.error(f"显示预览窗口时出错: {str(e)}")
            raise RuntimeError(f"显示预览窗口失败: {str(e)}")
    
    def _set_preview_window_title(self, file_path):
        """
        设置预览窗口标题 - 统一窗口标题设置规则
        
        Args:
            file_path (str): PPT文件路径
        """
        try:
            if not self.preview_window:
                raise ValueError("预览窗口不存在")
            
            # 统一窗口标题设置规则
            file_name = os.path.basename(file_path)
            window_title = f"预览修改 - {file_name}"
            
            self.preview_window.setWindowTitle(window_title)
            logging.debug(f"设置预览窗口标题: {window_title}")
            
        except Exception as e:
            logging.warning(f"设置预览窗口标题时出错: {str(e)}")
            # 设置默认标题
            if self.preview_window:
                self.preview_window.setWindowTitle("预览修改")
    
    def process_for_offline_preview(self):
        """处理PPT并保存结果，用于后续离线预览"""
        file_path = self.ppt_file_edit.text()
        folder_path = self.folder_path_edit.text()
        output_path = self.output_path_edit.text()
        slide_range = self.slide_range_edit.text()
        api_keys_file = self.api_keys_edit.text()
        prompt = self.prompt_edit.text()
        min_text_length = self.min_length_edit.value()
        
        # 验证输入 - 使用标准化错误处理保持向后兼容性
        try:
            if not folder_path and not file_path:
                raise FileValidationError('请选择PPT文件或文件夹')
            
            if not output_path:
                raise FileValidationError('请选择输出文件夹')
            
            if not os.path.exists(api_keys_file):
                raise FileValidationError(f'API密钥文件不存在: {api_keys_file}')
                
        except FileValidationError as e:
            ErrorHandler.handle_legacy_error(self, str(e), '警告', 'warning')
            return
        
        # 获取要处理的文件列表
        file_paths = self.get_selected_files()
        if not file_paths:
            return
        
        # 初始化API密钥管理器和文本处理器
        api_key_manager = APIKeyManager(api_keys_file)
        text_processor = TextProcessor(api_key_manager, prompt, self.model_config_manager, min_text_length)
        
        # 准备进度显示
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("开始处理PPT文件...")
        self.stop_button.setEnabled(True)
        
        # 创建一个标志变量表示是否取消处理
        self.processing_canceled = False
        
        # 创建工作线程处理PPT文件
        self.offline_preview_worker = OfflinePreviewWorker(
            file_paths, 
            slide_range, 
            api_key_manager,
            text_processor,
            output_path
        )
        
        # 连接信号
        self.offline_preview_worker.progress_updated.connect(self.update_offline_preview_progress)
        self.offline_preview_worker.preview_completed.connect(self.handle_offline_preview_completed)
        self.offline_preview_worker.preview_stopped.connect(self.handle_offline_preview_stopped)
        
        # 启动工作线程
        self.offline_preview_worker.start()
    
    def handle_offline_preview_completed(self, result_files, preview_files):
        """处理离线预览完成的回调"""
        # 重置进度显示
        self.progress_bar.setVisible(False)
        self.stop_button.setEnabled(False)
        
        if not preview_files:
            self.progress_label.setText("处理失败，未生成任何预览文件。")
            QMessageBox.warning(self, "警告", "处理失败，未生成任何预览文件。")
            return
        
        # 计算新生成和已存在的文件数量
        new_files = []
        existing_files = []
        
        for preview_file in preview_files:
            file_name = os.path.basename(preview_file)
            # 跳过selective文件
            if "_selective" in file_name.lower():
                logging.info(f"跳过selective文件: {preview_file}")
                continue
                
            # 检查是否是新生成的文件（带时间戳）
            if "_" in file_name and file_name.startswith("preview_"):
                name_parts = file_name.split("_")
                if len(name_parts) > 2 and re.search(r"\d{8}_\d{6}", file_name):
                    new_files.append(preview_file)
                else:
                    existing_files.append(preview_file)
            else:
                existing_files.append(preview_file)
        
        # 如果过滤后没有可用的预览文件，则显示警告
        if not new_files and not existing_files:
            self.progress_label.setText("没有找到可用的预览文件（跳过了selective文件）。")
            QMessageBox.warning(self, "警告", "没有找到可用的预览文件。\n注意：selective类型的文件已被跳过。")
            return
            
        # 显示处理结果
        total_selected = len(self.get_selected_files())
        message = f"处理完成！共处理 {len(preview_files)}/{total_selected} 个文件:\n\n"
        
        if new_files:
            message += f"新生成的预览文件 ({len(new_files)}):\n"
            for file in new_files[:3]:  # 只显示前3个
                message += f"- {os.path.basename(file)}\n"
            if len(new_files) > 3:
                message += f"... 等 {len(new_files)} 个文件\n"
            message += "\n"
        
        if existing_files:
            message += f"使用已存在的预览文件 ({len(existing_files)}):\n"
            for file in existing_files[:3]:  # 只显示前3个
                message += f"- {os.path.basename(file)}\n"
            if len(existing_files) > 3:
                message += f"... 等 {len(existing_files)} 个文件\n"
        
        # 更新状态信息
        self.progress_label.setText(f"处理完成！新生成: {len(new_files)}，使用已有: {len(existing_files)}，共 {len(preview_files)}/{total_selected} 个文件。")
        
        # 显示是否打开预览
        reply = QMessageBox.question(
            self,
            "处理完成",
            message + "\n是否立即预览最新处理的文件?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes and (new_files or existing_files):
            # 优先预览新生成的文件，如果没有则预览已存在的文件
            if new_files:
                self.load_offline_preview_file(new_files[-1])  # 预览最新生成的文件
            else:
                self.load_offline_preview_file(existing_files[-1])  # 预览最后一个文件
            
            # 清空输入框，以便于下次预览其他文件
            if not self.processing_canceled:  # 如果不是用户主动取消的，才清空
                self.ppt_file_edit.clear()
                self.folder_path_edit.clear()
                self.output_path_edit.clear()
                logging.info("离线预览完成，已清空文件路径输入框以便于下次预览")
    
    def handle_offline_preview_stopped(self):
        """处理离线预览停止的回调"""
        # 重置进度显示
        self.progress_bar.setVisible(False)
        self.stop_button.setEnabled(False)
        self.progress_label.setText("处理已停止。")
        
        # 提示用户
        QMessageBox.information(self, "处理已停止", "离线预览处理已停止。")
    
    def load_offline_preview(self):
        """加载之前保存的处理结果进行预览"""
        # 打开文件选择对话框
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择预览文件",
            "",
            "预览文件 (preview_*.pptx);;处理结果 (*.json);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
        
        # 检查是否为selective文件，不允许加载
        if "_selective" in file_path.lower():
            logging.warning(f"检测到selective文件，不允许加载: {file_path}")
            QMessageBox.warning(self, "文件类型受限", 
                              "selective类型的文件不能通过此方式打开。\n请使用其他方式查看此文件。")
            return
        
        # 根据文件类型选择处理方式
        if file_path.lower().endswith('.pptx') and os.path.basename(file_path).startswith('preview_'):
            # 直接加载预览PPT文件
            self.load_offline_preview_file(file_path)
        elif file_path.lower().endswith('.json'):
            # 加载JSON文件
            self.load_offline_preview_json(file_path)
        else:
            QMessageBox.warning(self, "文件类型错误", "请选择预览PPT文件(preview_*.pptx)或处理结果文件(*.json)。")
    
    def load_offline_preview_file(self, preview_file_path):
        """加载预览PPT文件进行预览"""
        try:
            # 检查文件是否存在
            if not os.path.exists(preview_file_path):
                QMessageBox.critical(self, "错误", f"预览文件不存在: {preview_file_path}")
                return
                
            # 检查是否为selective文件，不允许加载
            file_name = os.path.basename(preview_file_path)
            if "_selective" in file_name.lower():
                logging.warning(f"检测到selective文件，不允许通过预览窗口加载: {preview_file_path}")
                QMessageBox.warning(self, "文件类型受限", 
                                  "selective类型的文件不能通过此方式打开。\n请使用其他方式查看此文件。")
                return
            
            # 准备进度显示
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText(f"正在加载预览文件: {os.path.basename(preview_file_path)}...")
            
            # 创建预览窗口
            api_key_manager = self.create_api_key_manager()
            
            # 获取提示和最小文本长度
            prompt = self.prompt_edit.text()
            min_text_length = self.min_length_edit.value()
            
            # 创建文本处理器
            text_processor = TextProcessor(api_key_manager, prompt, self.model_config_manager, min_text_length)
            
            # 每次都创建新的预览窗口，防止文件混淆
            # 如果之前的预览窗口存在，关闭它
            if hasattr(self, 'preview_window') and self.preview_window:
                try:
                    self.preview_window.close()
                except:
                    pass
            
            # 使用统一的预览窗口创建逻辑
            self.preview_window = self._create_preview_window(api_key_manager, text_processor)
            
            # 设置窗口标题
            self._set_preview_window_title(preview_file_path)
            
            # 加载文件到预览窗口
            self.preview_window.load_ppt(preview_file_path)
            logging.info(f"正在打开预览文件: {preview_file_path}")
            
            # 显示预览窗口
            self._show_preview_window()
            self.preview_window.raise_()  # 确保窗口在最前面
            
            # 完成进度显示
            self.progress_bar.setValue(100)
            self.progress_label.setText("预览已准备就绪")
            QApplication.processEvents()
            
            # 短暂延迟后隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))
            
            # 清空三个输入框，以便于下次预览其他文件
            # 先保存当前文件名作为参考
            current_file = os.path.basename(preview_file_path)
            self.ppt_file_edit.clear()
            self.folder_path_edit.clear()
            self.output_path_edit.clear()
            logging.info(f"已加载预览文件 {current_file} 并清空路径输入框以便于下次预览")
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.progress_label.setText(f"加载预览失败: {str(e)}")
            logging.error(f"加载离线预览失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载预览失败: {str(e)}")
    
    def load_offline_preview_json(self, json_file_path):
        """加载JSON文件进行预览"""
        try:
            # 检查文件是否存在
            if not os.path.exists(json_file_path):
                QMessageBox.critical(self, "错误", f"JSON文件不存在: {json_file_path}")
                return
            
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取预览文件路径
            preview_file = data.get("preview_file")
            if not preview_file or not os.path.exists(preview_file):
                # 尝试从文件名推断预览文件
                dir_path = os.path.dirname(json_file_path)
                base_name = os.path.basename(json_file_path)
                if "_processing_results_" in base_name:
                    # 从结果文件名推断预览文件名
                    parts = base_name.split("_processing_results_")
                    if len(parts) == 2:
                        file_prefix = parts[0]
                        timestamp = parts[1].split(".")[0]
                        preview_file_name = f"preview_{file_prefix}_{timestamp}.pptx"
                        preview_file = os.path.join(dir_path, preview_file_name)
                
                if not preview_file or not os.path.exists(preview_file):
                    QMessageBox.critical(self, "错误", f"无法找到对应的预览文件。JSON文件: {json_file_path}")
                    return
            
            # 准备进度显示
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.progress_label.setText(f"正在加载预览文件: {os.path.basename(preview_file)}...")
            
            # 创建预览窗口
            api_key_manager = self.create_api_key_manager()
            
            # 获取提示和最小文本长度
            prompt = self.prompt_edit.text()
            min_text_length = self.min_length_edit.value()
            
            # 创建文本处理器
            text_processor = TextProcessor(api_key_manager, prompt, self.model_config_manager, min_text_length)
            
            # 每次都创建新的预览窗口，防止文件混淆
            # 如果之前的预览窗口存在，关闭它
            if hasattr(self, 'preview_window') and self.preview_window:
                try:
                    self.preview_window.close()
                except:
                    pass
            
            # 使用统一的预览窗口创建逻辑
            self.preview_window = self._create_preview_window(api_key_manager, text_processor)
            
            # 设置窗口标题
            self._set_preview_window_title(preview_file)
            
            # 加载文件到预览窗口
            self.preview_window.load_ppt(preview_file)
            logging.info(f"正在打开预览文件: {preview_file}")
            
            # 显示预览窗口
            self._show_preview_window()
            
            # 完成进度显示
            self.progress_bar.setValue(100)
            self.progress_label.setText("预览已准备就绪")
            QApplication.processEvents()
            
            # 短暂延迟后隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))
            
            # 清空三个输入框，以便于下次预览其他文件
            # 先保存当前文件名作为参考
            current_file = os.path.basename(preview_file)
            self.ppt_file_edit.clear()
            self.folder_path_edit.clear()
            self.output_path_edit.clear()
            logging.info(f"已加载预览文件 {current_file} 并清空路径输入框以便于下次预览")
            
        except Exception as e:
            self.progress_bar.setVisible(False)
            self.progress_label.setText(f"加载预览失败: {str(e)}")
            logging.error(f"加载离线预览失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载预览失败: {str(e)}")
    
    def update_offline_preview_progress(self, progress, file_index, total_files):
        """更新离线预览处理的进度显示"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(f"正在处理第 {file_index}/{total_files} 个PPT, 进度: {progress}%")
        # 处理UI事件，确保界面响应
        QApplication.processEvents()
    
    def clear_api_cache(self):
        """清除API缓存目录中的文件"""
        try:
            # 获取当前工作目录
            current_dir = os.getcwd()
            # 构建缓存目录路径
            cache_dir = os.path.join(current_dir, '.api_cache')
            
            if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
                # 获取缓存文件列表和总大小
                cache_files = []
                total_size = 0
                
                # 遍历缓存目录及其子目录
                for root, dirs, files in os.walk(cache_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            total_size += file_size
                            cache_files.append(file_path)
                        except Exception as e:
                            logging.warning(f"无法获取文件大小 {file_path}: {str(e)}")
                
                files_count = len(cache_files)
                
                if files_count == 0:
                    QMessageBox.information(self, '清除缓存', '缓存目录为空，无需清除。')
                    return
                
                # 将字节大小转换为人类可读格式
                size_str = self.format_file_size(total_size)
                
                # 显示确认对话框
                reply = QMessageBox.question(
                    self, 
                    '确认清除缓存',
                    f'确定要清除所有缓存文件吗？\n\n共有 {files_count} 个文件，占用空间 {size_str}。',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No  # 默认选择"否"，防止误操作
                )
                
                if reply == QMessageBox.No:
                    return
                
                # 显示进度对话框
                progress = QProgressDialog("正在清除缓存文件...", "取消", 0, files_count, self)
                progress.setWindowTitle("清除缓存")
                progress.setWindowModality(Qt.WindowModal)
                progress.show()
                
                # 删除所有缓存文件
                deleted_count = 0
                for i, file_path in enumerate(cache_files):
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                    except Exception as e:
                        logging.error(f"删除缓存文件 {file_path} 失败: {str(e)}")
                    
                    # 更新进度
                    progress.setValue(i + 1)
                    QApplication.processEvents()
                    
                    # 检查用户是否取消操作
                    if progress.wasCanceled():
                        break
                
                # 关闭进度对话框
                progress.close()
                
                # 记录日志
                logging.info(f"已清除 {deleted_count} 个缓存文件，总大小 {size_str}")
                
                # 显示成功消息
                QMessageBox.information(self, '清除缓存', f'已成功清除 {deleted_count} 个缓存文件，释放空间 {size_str}。')
            else:
                QMessageBox.information(self, '清除缓存', '缓存目录不存在。')
        except Exception as e:
            # 记录错误日志
            logging.error(f"清除缓存失败: {str(e)}")
            # 显示错误消息
            QMessageBox.critical(self, '错误', f'清除缓存时发生错误: {str(e)}')

    def format_file_size(self, size_bytes):
        """将字节大小转换为人类可读格式"""
        if size_bytes < 1024:
            return f"{size_bytes} 字节"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

    def view_api_cache(self):
        """查看API缓存信息"""
        try:
            # 获取当前工作目录
            current_dir = os.getcwd()
            # 构建缓存目录路径
            cache_dir = os.path.join(current_dir, '.api_cache')
            
            if not os.path.exists(cache_dir) or not os.path.isdir(cache_dir):
                QMessageBox.information(self, '缓存信息', '缓存目录不存在。')
                return
            
            # 收集缓存信息
            cache_files = []
            total_size = 0
            cache_extensions = {}
            oldest_file_time = None
            newest_file_time = None
            
            # 遍历缓存目录及其子目录
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        # 获取文件大小
                        file_size = os.path.getsize(file_path)
                        total_size += file_size
                        
                        # 获取文件修改时间
                        mod_time = os.path.getmtime(file_path)
                        if oldest_file_time is None or mod_time < oldest_file_time:
                            oldest_file_time = mod_time
                        if newest_file_time is None or mod_time > newest_file_time:
                            newest_file_time = mod_time
                        
                        # 统计文件扩展名
                        ext = os.path.splitext(file)[1].lower()
                        if ext in cache_extensions:
                            cache_extensions[ext][0] += 1
                            cache_extensions[ext][1] += file_size
                        else:
                            cache_extensions[ext] = [1, file_size]
                        
                        cache_files.append(file_path)
                    except Exception as e:
                        logging.warning(f"获取文件信息失败 {file_path}: {str(e)}")
            
            if not cache_files:
                QMessageBox.information(self, '缓存信息', '缓存目录为空。')
                return
            
            # 格式化信息
            info_text = "API缓存信息统计：\n\n"
            info_text += f"缓存文件总数: {len(cache_files)} 个\n"
            info_text += f"缓存总大小: {self.format_file_size(total_size)}\n"
            
            if oldest_file_time and newest_file_time:
                info_text += f"最早缓存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(oldest_file_time))}\n"
                info_text += f"最新缓存时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(newest_file_time))}\n"
            
            if cache_extensions:
                info_text += "\n文件类型统计:\n"
                for ext, (count, size) in sorted(cache_extensions.items(), key=lambda x: x[1][1], reverse=True):
                    ext_name = ext if ext else "无扩展名"
                    info_text += f"  {ext_name}: {count}个文件, 共{self.format_file_size(size)}\n"
            
            # 显示信息对话框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("缓存信息")
            msg_box.setText(info_text)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setDefaultButton(QMessageBox.Ok)
            msg_box.exec_()
            
        except Exception as e:
            # 记录错误日志
            logging.error(f"查看缓存信息失败: {str(e)}")
            # 显示错误消息
            QMessageBox.critical(self, '错误', f'查看缓存信息时发生错误: {str(e)}')

    def detect_related_folders(self, folder_path):
        """
        根据文件夹命名规律自动检测相关联的输入和输出文件夹
        
        Args:
            folder_path: 已知的文件夹路径
            
        Returns:
            (input_folder, output_folder): 检测到的输入文件夹和输出文件夹的元组
        """
        # 初始化返回值
        input_folder = None
        output_folder = None
        
        try:
            # 获取文件夹名和父目录
            folder_name = os.path.basename(folder_path)
            parent_dir = os.path.dirname(folder_path)
            
            # 情况1: folder_path是输入文件夹(folder_XX格式)
            if folder_name.startswith("folder_"):
                # 提取XX部分
                folder_num = folder_name[7:]  # 移除"folder_"
                if folder_num.isdigit():
                    input_folder = folder_path
                    
                    # 查找同级目录下的XX-Y格式文件夹作为输出文件夹
                    pattern = f"{folder_num}-*"
                    potential_outputs = glob.glob(os.path.join(parent_dir, pattern))
                    
                    # 如果找到多个匹配的输出文件夹，按照数字顺序选择最新的一个
                    if potential_outputs:
                        # 按照文件夹名中的数字部分排序
                        sorted_outputs = sorted(potential_outputs, 
                                               key=lambda p: int(os.path.basename(p).split('-')[1]) 
                                               if os.path.basename(p).split('-')[1].isdigit() else 0,
                                               reverse=True)
                        output_folder = sorted_outputs[0]
                        logging.info(f"检测到输入文件夹 {input_folder} 对应的输出文件夹: {output_folder}")
            
            # 情况2: folder_path是输出文件夹(XX-Y格式)
            elif "-" in folder_name:
                parts = folder_name.split("-")
                if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                    folder_num = parts[0]
                    output_folder = folder_path
                    
                    # 查找同级目录下的folder_XX格式文件夹作为输入文件夹
                    input_pattern = f"folder_{folder_num}"
                    potential_input = os.path.join(parent_dir, input_pattern)
                    
                    if os.path.exists(potential_input) and os.path.isdir(potential_input):
                        input_folder = potential_input
                        logging.info(f"检测到输出文件夹 {output_folder} 对应的输入文件夹: {input_folder}")
        
        except Exception as e:
            logging.error(f"检测相关文件夹时出错: {str(e)}")
        
        return input_folder, output_folder 

    def get_preview_style(self):
        """
        获取预览窗口的样式表 - 统一预览窗口样式设置
        
        重构说明：
        - 作为预览功能统一化的一部分，确保所有预览窗口使用相同的样式
        - 提供一致的用户界面体验
        - 集中管理预览窗口的视觉样式
        - 便于后续样式维护和更新
        
        Returns:
            str: 预览窗口的CSS样式表字符串
            
        需求对应: 4.2 (统一窗口标志和样式设置)
        """
        return """
        QWidget {
            font-family: 'Microsoft YaHei', Arial;
            font-size: 9pt;
            background-color: #2B2B2B;
            color: #FFFFFF;
        }
        QLabel {
            padding: 3px;
            color: #FFFFFF;
        }
        QLineEdit, QSpinBox, QTextEdit {
            padding: 5px;
            border: 1px solid #3C3F41;
            border-radius: 3px;
            background-color: #3C3F41;
            color: #FFFFFF;
            min-height: 20px;
            selection-background-color: #2196F3;
        }
        QPushButton {
            padding: 8px;
            border: 1px solid #2196F3;
            border-radius: 3px;
            background-color: #2196F3;
            color: white;
            min-width: 80px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #1976D2;
            border: 1px solid #1976D2;
        }
        """

    # 添加随机预览未审阅文件的方法
    def random_preview_unreviewed_file(self):
        """
        随机预览未审阅文件功能 - 保持现有行为作为参考标准
        
        重构说明：
        - 此功能保持不变，作为其他两个预览功能的参考标准
        - 继续使用preview_file()函数进行最终预览
        - 保持现有的搜索和选择逻辑
        - 保持现有的进度显示和错误处理
        
        处理流程：
        1. 验证输出文件夹路径
        2. 搜索所有preview_开头的PPT文件
        3. 筛选出未审阅的文件（没有对应的_selective文件）
        4. 随机选择一个未审阅文件
        5. 调用preview_file()函数进行预览
        6. 显示进度信息和处理结果
        
        错误处理：
        - 文件夹路径验证错误
        - 文件搜索和筛选错误
        - 预览功能调用错误
        
        需求对应: 3.1, 3.2, 3.3
        """
        # 获取输出文件夹路径
        output_folder = self.output_path_edit.text().strip()
        
        if not output_folder or not os.path.isdir(output_folder):
            ErrorHandler.handle_legacy_error(self, "请指定有效的输出文件夹路径！", "文件夹错误", "warning")
            return
        
        logging.info(f"开始搜索未审阅文件，输出文件夹: {output_folder}")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        self.progress_label.setText("正在搜索未审阅文件...")
        QApplication.processEvents()
        
        try:
            # 查找所有preview文件
            all_preview_files = []
            for root, _, files in os.walk(output_folder):
                for file in files:
                    if file.startswith("preview_") and file.endswith(".pptx") and "_selective" not in file:
                        all_preview_files.append(os.path.join(root, file))
            
            self.progress_bar.setValue(40)
            self.progress_label.setText(f"找到 {len(all_preview_files)} 个预览文件，正在筛选未审阅文件...")
            QApplication.processEvents()
            
            if not all_preview_files:
                self.progress_bar.setVisible(False)
                self.progress_label.setText("未找到任何预览文件")
                QMessageBox.information(self, "搜索结果", "在输出文件夹中未找到任何预览文件。")
                return
            
            # 筛选出未审阅的文件（没有对应的_selective文件）
            unreviewed_files = []
            for preview_file in all_preview_files:
                # 构造对应的selective文件路径
                selective_file = os.path.splitext(preview_file)[0] + "_selective.pptx"
                
                # 如果selective文件不存在，则认为是未审阅文件
                if not os.path.exists(selective_file):
                    unreviewed_files.append(preview_file)
            
            self.progress_bar.setValue(80)
            self.progress_label.setText(f"找到 {len(unreviewed_files)} 个未审阅文件，准备随机选择...")
            QApplication.processEvents()
            
            if not unreviewed_files:
                self.progress_bar.setVisible(False)
                self.progress_label.setText("所有文件已审阅")
                QMessageBox.information(self, "搜索结果", "所有预览文件都已经审阅完毕！")
                return
            
            # 随机选择一个未审阅文件
            selected_file = random.choice(unreviewed_files)
            logging.info(f"随机选择未审阅文件: {selected_file}")
            
            self.progress_bar.setValue(100)
            self.progress_label.setText(f"已选择文件: {os.path.basename(selected_file)}")
            QApplication.processEvents()
            
            # 设置文件路径并打开预览
            self.ppt_file_edit.setText(selected_file)
            self.preview_file(selected_file)
            
        except Exception as e:
            logging.exception(f"随机预览未审阅文件时出错: {str(e)}")
            self.progress_bar.setVisible(False)
            self.progress_label.setText("处理出错")
            QMessageBox.critical(self, "错误", f"随机预览未审阅文件时出错: {str(e)}")

    def on_provider_changed(self, index):
        """提供商改变时更新模型列表"""
        provider_id = self.provider_combo.itemData(index)
        self.model_combo.clear()
        
        # 从配置加载模型
        models = self.model_config_file.get_models(provider_id)
        for model in models:
            self.model_combo.addItem(model.get("name", ""), model.get("id", ""))
        
        # 显示OpenRouter特殊设置
        self.openrouter_settings.setVisible(provider_id == "openrouter")
        
        # 从配置文件同步API Key
        provider_config = self.model_config_file.get_provider(provider_id)
        if provider_config and provider_id == "openrouter":
            # 同步API Key到隐藏字段
            if "api_key" in provider_config:
                api_key = provider_config.get("api_key", "")
                self.openrouter_api_edit.setText(api_key)
                # 同时更新model_config_manager中的API Key
                if hasattr(self, 'model_config_manager'):
                    self.model_config_manager.openrouter_api_key = api_key
                    logging.info(f"已切换到OpenRouter提供商并同步API Key (长度: {len(api_key)})")
        
        # 选择之前选择的模型
        current_model = self.settings.value("model_id", "", str)
        model_index = -1
        for i in range(self.model_combo.count()):
            model_id = self.model_combo.itemData(i)
            if model_id == current_model:
                model_index = i
                break
        
        # 更新combobox但不触发事件
        if model_index >= 0:
            # 暂时断开信号连接
            self.model_combo.blockSignals(True)
            self.model_combo.setCurrentIndex(model_index)
            self.model_combo.blockSignals(False)
        
        # 更新模型配置，不管找没找到之前的模型
        if hasattr(self, 'model_config_manager'):
            model_id = self.model_combo.itemData(self.model_combo.currentIndex()) if self.model_combo.count() > 0 else ""
            self.model_config_manager.set_active_model(provider_id, model_id)
            logging.info(f"提供商改变: 已设置当前模型配置 - 提供商: {provider_id}, 模型: {model_id}")

    def on_model_changed(self, index):
        """模型改变时更新配置"""
        if index < 0 or not hasattr(self, 'model_config_manager'):
            return
            
        provider_id = self.provider_combo.itemData(self.provider_combo.currentIndex())
        model_id = self.model_combo.itemData(index)
        
        # 更新模型配置管理器
        self.model_config_manager.set_active_model(provider_id, model_id)
        
        # 记录更新
        logging.info(f"模型改变: 已设置当前模型配置 - 提供商: {provider_id}, 模型: {model_id}")
    
    def edit_model_config(self):
        """打开模型配置对话框"""
        # 在打开对话框前，先将主界面的API Key同步到模型配置中
        provider_id = self.provider_combo.itemData(self.provider_combo.currentIndex())
        if provider_id == "openrouter":
            # 获取当前OpenRouter提供商配置
            provider_config = self.model_config_file.get_provider("openrouter")
            if provider_config:
                # 从model_config_manager获取最新的API Key
                api_key = self.model_config_manager.openrouter_api_key
                # 更新到配置文件
                provider_config["api_key"] = api_key
                # 保存配置
                self.model_config_file.save_config()
                logging.info(f"已将OpenRouter API Key同步到配置文件 (长度: {len(api_key) if api_key else 0})")
        
        # 打开模型配置对话框
        dialog = ModelConfigDialog(self.model_config_file, self)
        if dialog.exec_() == QDialog.Accepted:
            # 记住当前选中的提供商
            current_provider = self.provider_combo.itemData(self.provider_combo.currentIndex())
            
            # 重新加载提供商列表
            self.provider_combo.clear()
            for provider_id, provider in self.model_config_file.get_providers().items():
                self.provider_combo.addItem(provider.get("name", provider_id), provider_id)
            
            # 恢复之前选择的提供商
            for i in range(self.provider_combo.count()):
                if self.provider_combo.itemData(i) == current_provider:
                    self.provider_combo.setCurrentIndex(i)
                    break
            
            # 刷新模型列表
            self.on_provider_changed(self.provider_combo.currentIndex())
            
            # 手动同步当前选中提供商的API Key
            provider_id = self.provider_combo.itemData(self.provider_combo.currentIndex())
            provider_config = self.model_config_file.get_provider(provider_id)
            
            if provider_config and provider_id == "openrouter":
                # 将API Key从配置同步到隐藏字段
                if "api_key" in provider_config:
                    api_key = provider_config.get("api_key", "")
                    self.openrouter_api_edit.setText(api_key)
                    # 同时更新model_config_manager中的API Key
                    self.model_config_manager.openrouter_api_key = api_key
                    
                    # 输出日志记录同步结果
                    if api_key:
                        logging.info(f"已从配置同步API Key到主界面 (长度: {len(api_key)})")
                    else:
                        logging.warning("从配置同步的API Key为空")
            
            # 强制刷新文本处理器配置
            self.create_text_processor()
    
    def closeEvent(self, event):
        """窗口关闭事件，保存设置"""
        # 保存设置
        self.settings.setValue("min_text_length", self.min_length_edit.value())
        self.settings.setValue("api_keys_file", self.api_keys_edit.text())
        self.settings.setValue("prompt", self.prompt_edit.text())
        
        # 保存模型设置
        provider = self.provider_combo.itemData(self.provider_combo.currentIndex())
        self.settings.setValue("model_provider", provider)
        
        if provider == "openrouter":
            self.settings.setValue("openrouter_api_key", self.openrouter_api_edit.text())
            self.settings.setValue("openrouter_site_url", self.openrouter_url_edit.text())
            self.settings.setValue("openrouter_site_name", self.openrouter_name_edit.text())
        
        model_id = self.model_combo.itemData(self.model_combo.currentIndex()) if self.model_combo.count() > 0 else ""
        self.settings.setValue("model_id", model_id)
        
        # 如果工作线程正在运行，请求停止
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.request_stop()
            self.worker_thread.wait(1000)  # 等待最多1秒钟
        
        event.accept()