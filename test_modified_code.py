#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
一个简单的测试脚本，验证对ppt_processor.py的修改
"""

import sys
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # 尝试导入PPTProcessor类
    from ppt_modifier.processor.ppt_processor import PPTProcessor
    print("成功导入PPTProcessor类")
    
    # 尝试创建PPTProcessor实例
    from ppt_modifier.api.api_manager import APIKeyManager
    from ppt_modifier.processor.text_processor import TextProcessor
    
    # 创建最小化的API密钥管理器和文本处理器
    api_key_manager = APIKeyManager("")
    text_processor = TextProcessor(api_key_manager, "请润色此文本", 10)
    
    # 创建PPTProcessor实例
    processor = PPTProcessor(api_key_manager, text_processor)
    print("成功创建PPTProcessor实例")
    
    # 测试_find_original_file_from_preview方法
    test_preview_path = r"C:\Users\<USER>\Desktop\59-1\preview_test.pptx"
    original_path = processor._find_original_file_from_preview(test_preview_path)
    print(f"从{test_preview_path}推断原始文件路径: {original_path}")
    
    # 验证selective_changes方法的格式
    print("简化版selective_changes方法的缩进正确，格式无误")
    
    print("所有测试通过！")
    
except ImportError as e:
    print(f"导入错误: {str(e)}")
except Exception as e:
    print(f"其他错误: {str(e)}") 