import os
import re
import difflib
import logging
from pptx import Presentation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 定义要查找的目标文本
TARGET_TEXT = "不对，因为根的生长不仅仅依赖伸长区细胞体积的不断增大，还需要依靠分生区细胞的不断分裂来增加细胞数量。"

def normalize_text(text):
    """标准化文本，去除多余空格"""
    if not text:
        return ""
    return re.sub(r'\s+', ' ', text).strip()

def simplify_text(text):
    """简化文本，去除标点符号和空格，转为小写"""
    if not text:
        return ""
    text = re.sub(r'\s+', ' ', text).strip()
    text = re.sub(r'[,.;:!?，。；：！？]', '', text)
    return text.lower()

def find_text_in_ppt(ppt_path):
    """在PPT中查找目标文本
    
    Args:
        ppt_path: PPT文件路径
    """
    logging.info(f"打开PPT文件: {ppt_path}")
    try:
        prs = Presentation(ppt_path)
    except Exception as e:
        logging.error(f"无法打开PPT文件: {str(e)}")
        return
    
    logging.info(f"成功加载PPT文件，共 {len(prs.slides)} 张幻灯片")
    
    # 预处理目标文本
    target_normalized = normalize_text(TARGET_TEXT)
    target_simplified = simplify_text(TARGET_TEXT)
    logging.info(f"目标文本: '{TARGET_TEXT}'")
    logging.info(f"标准化后: '{target_normalized}'")
    
    # 跟踪所有找到的匹配
    all_matches = []
    
    # 跟踪每个文本框的原始内容和结构
    all_text_frames = []
    
    # 遍历每张幻灯片
    for slide_idx, slide in enumerate(prs.slides):
        slide_num = slide_idx + 1
        logging.info(f"检查幻灯片 {slide_num}")
        
        # 检查该幻灯片的每个形状
        for shape_idx, shape in enumerate(slide.shapes):
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            if not text_frame.text.strip():
                continue
            
            # 获取文本框完整内容
            full_text = text_frame.text
            full_normalized = normalize_text(full_text)
            full_simplified = simplify_text(full_text)
            
            # 保存文本框结构信息
            text_frame_info = {
                'slide': slide_num,
                'shape': shape_idx,
                'text': full_text,
                'paragraphs': []
            }
            
            # 检查完整文本的匹配情况
            similarity = difflib.SequenceMatcher(None, target_normalized, full_normalized).ratio()
            contains_target = target_normalized in full_normalized
            
            if similarity > 0.6 or contains_target:
                logging.info(f"在幻灯片 {slide_num} 找到可能的匹配")
                logging.info(f"文本框内容: '{full_text[:150]}{'...' if len(full_text)>150 else ''}'")
                logging.info(f"相似度: {similarity:.2f}, 包含匹配: {contains_target}")
                
                all_matches.append({
                    'slide': slide_num,
                    'shape': shape_idx,
                    'text': full_text,
                    'similarity': similarity,
                    'contains': contains_target
                })
            
            # 深入检查每个段落
            for para_idx, para in enumerate(text_frame.paragraphs):
                para_text = para.text
                para_normalized = normalize_text(para_text)
                
                # 保存段落信息
                para_info = {
                    'index': para_idx,
                    'text': para_text,
                    'runs': []
                }
                
                # 计算段落级别的相似度
                para_similarity = difflib.SequenceMatcher(None, target_normalized, para_normalized).ratio()
                para_contains = target_normalized in para_normalized
                
                if para_similarity > 0.6 or para_contains:
                    logging.info(f"在幻灯片 {slide_num} 段落 #{para_idx+1} 找到匹配")
                    logging.info(f"段落内容: '{para_text}'")
                    logging.info(f"段落相似度: {para_similarity:.2f}, 包含匹配: {para_contains}")
                    
                    all_matches.append({
                        'slide': slide_num,
                        'shape': shape_idx,
                        'paragraph': para_idx,
                        'text': para_text,
                        'similarity': para_similarity,
                        'contains': para_contains
                    })
                
                # 检查每个运行块
                for run_idx, run in enumerate(para.runs):
                    run_text = run.text
                    
                    # 保存运行块信息
                    para_info['runs'].append({
                        'index': run_idx,
                        'text': run_text
                    })
                    
                    # 如果运行块长度大于10，检查相似度
                    if len(run_text) > 10:
                        run_normalized = normalize_text(run_text)
                        run_similarity = difflib.SequenceMatcher(None, target_normalized, run_normalized).ratio()
                        run_contains = target_normalized in run_normalized
                        
                        if run_similarity > 0.5 or run_contains:
                            logging.info(f"在幻灯片 {slide_num} 段落 #{para_idx+1} 运行块 #{run_idx+1} 找到匹配")
                            logging.info(f"运行块内容: '{run_text}'")
                            logging.info(f"运行块相似度: {run_similarity:.2f}, 包含匹配: {run_contains}")
                            
                            all_matches.append({
                                'slide': slide_num,
                                'shape': shape_idx,
                                'paragraph': para_idx,
                                'run': run_idx,
                                'text': run_text,
                                'similarity': run_similarity,
                                'contains': run_contains
                            })
                
                text_frame_info['paragraphs'].append(para_info)
            
            all_text_frames.append(text_frame_info)
    
    # 总结结果
    if all_matches:
        logging.info(f"\n找到 {len(all_matches)} 个潜在匹配")
        
        # 对匹配结果按相似度排序
        all_matches.sort(key=lambda x: x['similarity'], reverse=True)
        
        # 打印前3个最佳匹配
        for i, match in enumerate(all_matches[:3]):
            logging.info(f"\n最佳匹配 #{i+1}:")
            logging.info(f"幻灯片: {match['slide']}")
            if 'paragraph' in match:
                logging.info(f"段落: {match['paragraph']+1}")
            if 'run' in match:
                logging.info(f"运行块: {match['run']+1}")
            logging.info(f"相似度: {match['similarity']:.2f}")
            logging.info(f"内容: '{match['text']}'")
    else:
        logging.info("未找到任何匹配")
    
    # 打印文本框的详细结构（针对较高相似度的幻灯片）
    matched_slides = {match['slide'] for match in all_matches if match['similarity'] > 0.7}
    if matched_slides:
        logging.info("\n\n===== 相关幻灯片的文本结构分析 =====")
        
        for slide_num in matched_slides:
            logging.info(f"\n幻灯片 {slide_num} 的文本结构:")
            slide_frames = [tf for tf in all_text_frames if tf['slide'] == slide_num]
            
            for i, frame in enumerate(slide_frames):
                logging.info(f"文本框 #{i+1}:")
                logging.info(f"完整文本: '{frame['text'][:100]}{'...' if len(frame['text'])>100 else ''}'")
                
                for j, para in enumerate(frame['paragraphs']):
                    logging.info(f"  段落 #{j+1}: '{para['text'][:80]}{'...' if len(para['text'])>80 else ''}'")
                    
                    if para['runs']:
                        for k, run in enumerate(para['runs']):
                            if run['text'].strip():
                                logging.info(f"    Run #{k+1}: '{run['text']}'")

def main():
    """主函数"""
    print("PPT特定文本查找工具")
    print(f"目标文本: '{TARGET_TEXT}'")
    
    # 获取PPT文件路径
    ppt_path = input("请输入PPT文件路径: ").strip()
    
    if not os.path.exists(ppt_path):
        print(f"错误: 文件不存在 - {ppt_path}")
        return
    
    # 执行查找
    find_text_in_ppt(ppt_path)
    
    print("\n查找完成。详细结果请查看日志输出。")

if __name__ == "__main__":
    main() 