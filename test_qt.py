"""
测试Qt环境
"""
import os
import sys
from PyQt5.QtCore import QLibraryInfo
from PyQt5.QtWidgets import QApplication, QLabel, QWidget

# 打印Qt相关路径信息
print("Qt安装路径:", QLibraryInfo.location(QLibraryInfo.LibrariesPath))
print("Qt插件路径:", QLibraryInfo.location(QLibraryInfo.PluginsPath))
print("当前环境变量QT_PLUGIN_PATH:", os.environ.get("QT_PLUGIN_PATH", "未设置"))

# 创建一个最小的Qt应用程序
app = QApplication(sys.argv)
window = QWidget()
window.setWindowTitle("Qt测试")
window.resize(300, 200)
label = QLabel("如果你能看到这个窗口，Qt环境正常！", window)
label.move(50, 80)
window.show()
sys.exit(app.exec_()) 