#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一致性测试脚本 - 验证三个预览功能的一致性
测试任务 6.2: 验证三个功能使用相同的preview_file()函数、预览窗口创建的一致性、错误处理的一致性
"""

import os
import sys
import logging
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch, MagicMock, call
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QMimeData, QUrl
from PyQt5.QtGui import QDropEvent

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ppt_modifier.gui.main_window import MainWindow, ErrorHandler
from ppt_modifier.api.api_manager import APIKeyManager
from ppt_modifier.processor.text_processor import TextProcessor


class ConsistencyTestCase(unittest.TestCase):
    """一致性测试用例"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        # 创建QApplication实例
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        cls.logger = logging.getLogger(__name__)
        cls.logger.info("开始一致性测试")
    
    def setUp(self):
        """设置每个测试"""
        # 创建临时目录和测试文件
        self.test_dir = tempfile.mkdtemp()
        self.test_ppt_file = os.path.join(self.test_dir, "test.pptx")
        self.preview_ppt_file = os.path.join(self.test_dir, "preview_test_20241201_120000.pptx")
        self.selective_ppt_file = os.path.join(self.test_dir, "test_selective.pptx")
        
        # 创建测试文件
        for file_path in [self.test_ppt_file, self.preview_ppt_file, self.selective_ppt_file]:
            with open(file_path, 'wb') as f:
                f.write(b'fake ppt content')
        
        # 创建主窗口实例
        self.main_window = MainWindow()
        
        # 设置必要的配置
        self.main_window.api_keys_edit.setText(os.path.join(self.test_dir, "api_keys.txt"))
        with open(os.path.join(self.test_dir, "api_keys.txt"), 'w') as f:
            f.write("test_api_key")
        
        self.main_window.output_path_edit.setText(self.test_dir)
        
        # Mock预览窗口
        self.mock_preview_window = Mock()
        self.mock_preview_window.isVisible.return_value = False
        self.mock_preview_window.show = Mock()
        self.mock_preview_window.raise_ = Mock()
        self.mock_preview_window.activateWindow = Mock()
        self.mock_preview_window.setFocus = Mock()
        self.mock_preview_window.setWindowTitle = Mock()
        self.mock_preview_window.load_ppt = Mock()
        
        self.logger.info(f"测试环境设置完成，测试目录: {self.test_dir}")
    
    def tearDown(self):
        """清理每个测试"""
        try:
            if hasattr(self.main_window, 'preview_window') and self.main_window.preview_window:
                self.main_window.preview_window.close()
            self.main_window.close()
        except:
            pass
        
        # 清理临时目录
        try:
            shutil.rmtree(self.test_dir)
        except:
            pass
    
    @classmethod
    def tearDownClass(cls):
        """清理测试类"""
        try:
            if hasattr(cls, 'app'):
                cls.app.quit()
        except:
            pass
    
    def test_preview_file_function_consistency(self):
        """测试三个功能都使用相同的preview_file()函数"""
        self.logger.info("开始测试preview_file函数一致性")
        
        # 创建调用追踪器
        call_tracker = []
        
        def track_preview_file(file_path):
            call_tracker.append(('preview_file', file_path))
            # 模拟preview_file的基本行为
            self.main_window.create_api_key_manager()
            self.main_window.create_text_processor()
        
        # 替换preview_file方法
        original_preview_file = self.main_window.preview_file
        self.main_window.preview_file = track_preview_file
        
        try:
            # 1. 测试拖放功能调用preview_file
            self._simulate_drag_drop()
            
            # 2. 测试预览修改按钮调用preview_file
            self._simulate_preview_button()
            
            # 验证preview_file被调用
            self.assertEqual(len(call_tracker), 2, "preview_file应该被调用2次")
            self.assertEqual(call_tracker[0][0], 'preview_file', "第一次调用应该是preview_file")
            self.assertEqual(call_tracker[1][0], 'preview_file', "第二次调用应该是preview_file")
            
            # 验证调用的文件路径正确
            self.assertEqual(call_tracker[0][1], self.test_ppt_file, "拖放功能应该调用正确的文件路径")
            self.assertEqual(call_tracker[1][1], self.test_ppt_file, "预览按钮功能应该调用正确的文件路径")
            
            self.logger.info("✓ preview_file函数一致性测试通过")
            
        finally:
            # 恢复原始方法
            self.main_window.preview_file = original_preview_file
    
    def test_preview_window_creation_consistency(self):
        """测试预览窗口创建的一致性"""
        self.logger.info("开始测试预览窗口创建一致性")
        
        # Mock预览窗口创建相关方法
        with patch.object(self.main_window, '_create_preview_window', return_value=self.mock_preview_window) as mock_create:
            with patch.object(self.main_window, '_cleanup_preview_window') as mock_cleanup:
                with patch.object(self.main_window, '_show_preview_window') as mock_show:
                    with patch.object(self.main_window, '_set_preview_window_title') as mock_title:
                        
                        # 测试三个功能的预览窗口创建
                        test_cases = [
                            ("drag_drop", self._simulate_drag_drop),
                            ("preview_button", self._simulate_preview_button),
                            ("direct_call", lambda: self.main_window.preview_file(self.test_ppt_file))
                        ]
                        
                        for test_name, test_func in test_cases:
                            self.logger.info(f"测试 {test_name} 的预览窗口创建")
                            
                            # 重置mock
                            mock_create.reset_mock()
                            mock_cleanup.reset_mock()
                            mock_show.reset_mock()
                            mock_title.reset_mock()
                            
                            # 执行测试
                            test_func()
                            
                            # 验证统一的预览窗口创建流程
                            mock_cleanup.assert_called_once()
                            mock_create.assert_called_once()
                            mock_show.assert_called_once()
                            mock_title.assert_called_once()
                            
                            # 验证创建参数的一致性
                            create_args = mock_create.call_args
                            self.assertEqual(len(create_args[0]), 2, f"{test_name}: 创建预览窗口应该传入2个参数")
                            
                            # 验证参数类型
                            api_manager, text_processor = create_args[0]
                            self.assertIsNotNone(api_manager, f"{test_name}: API管理器不能为空")
                            self.assertIsNotNone(text_processor, f"{test_name}: 文本处理器不能为空")
        
        self.logger.info("✓ 预览窗口创建一致性测试通过")
    
    def test_error_handling_consistency(self):
        """测试错误处理的一致性"""
        self.logger.info("开始测试错误处理一致性")
        
        # 测试文件不存在的错误处理
        non_existent_file = os.path.join(self.test_dir, "non_existent.pptx")
        
        error_test_cases = [
            ("preview_file", lambda: self.main_window.preview_file(non_existent_file)),
            ("preview_button", self._simulate_preview_button_error),
            ("drag_drop", self._simulate_drag_drop_error)
        ]
        
        for test_name, test_func in error_test_cases:
            self.logger.info(f"测试 {test_name} 的错误处理")
            
            with patch.object(QMessageBox, 'warning') as mock_warning:
                with patch.object(QMessageBox, 'critical') as mock_critical:
                    
                    # 执行测试
                    test_func()
                    
                    # 验证错误处理被调用
                    error_shown = mock_warning.called or mock_critical.called
                    self.assertTrue(error_shown, f"{test_name}: 应该显示错误消息")
        
        # 测试selective文件的错误处理一致性
        selective_test_cases = [
            ("preview_file", lambda: self.main_window.preview_file(self.selective_ppt_file)),
            ("drag_drop", self._simulate_drag_drop_selective)
        ]
        
        for test_name, test_func in selective_test_cases:
            self.logger.info(f"测试 {test_name} 的selective文件错误处理")
            
            with patch.object(QMessageBox, 'warning') as mock_warning:
                
                # 执行测试
                test_func()
                
                # 验证selective文件错误处理
                mock_warning.assert_called()
                
                # 验证错误消息内容包含selective相关信息
                warning_args = mock_warning.call_args
                error_message = warning_args[0][2]  # 第三个参数是错误消息
                self.assertIn("selective", error_message.lower(), 
                            f"{test_name}: 错误消息应该包含selective相关信息")
        
        self.logger.info("✓ 错误处理一致性测试通过")
    
    def test_api_manager_creation_consistency(self):
        """测试API管理器创建的一致性"""
        self.logger.info("开始测试API管理器创建一致性")
        
        # Mock API管理器创建
        with patch.object(self.main_window, 'create_api_key_manager') as mock_create_api:
            with patch.object(self.main_window, 'create_text_processor') as mock_create_text:
                with patch.object(self.main_window, '_create_preview_window', return_value=self.mock_preview_window):
                    with patch.object(self.main_window, '_cleanup_preview_window'):
                        with patch.object(self.main_window, '_show_preview_window'):
                            with patch.object(self.main_window, '_set_preview_window_title'):
                                
                                # 设置mock返回值
                                mock_api_manager = Mock()
                                mock_text_processor = Mock()
                                mock_create_api.return_value = mock_api_manager
                                mock_create_text.return_value = mock_text_processor
                                
                                # 测试三个功能的API管理器创建
                                test_cases = [
                                    ("drag_drop", self._simulate_drag_drop),
                                    ("preview_button", self._simulate_preview_button),
                                    ("direct_call", lambda: self.main_window.preview_file(self.test_ppt_file))
                                ]
                                
                                for test_name, test_func in test_cases:
                                    self.logger.info(f"测试 {test_name} 的API管理器创建")
                                    
                                    # 重置mock
                                    mock_create_api.reset_mock()
                                    mock_create_text.reset_mock()
                                    
                                    # 执行测试
                                    test_func()
                                    
                                    # 验证API管理器和文本处理器都被创建
                                    mock_create_api.assert_called_once()
                                    mock_create_text.assert_called_once()
        
        self.logger.info("✓ API管理器创建一致性测试通过")
    
    def test_window_lifecycle_consistency(self):
        """测试窗口生命周期管理的一致性"""
        self.logger.info("开始测试窗口生命周期管理一致性")
        
        # Mock窗口生命周期相关方法
        with patch.object(self.main_window, '_cleanup_preview_window') as mock_cleanup:
            with patch.object(self.main_window, '_create_preview_window', return_value=self.mock_preview_window) as mock_create:
                with patch.object(self.main_window, '_show_preview_window') as mock_show:
                    
                    # 测试窗口生命周期管理的顺序
                    self.main_window.preview_file(self.test_ppt_file)
                    
                    # 验证调用顺序：清理 -> 创建 -> 显示
                    expected_calls = [
                        call(),  # cleanup
                        call(),  # show
                    ]
                    
                    mock_cleanup.assert_called_once()
                    mock_create.assert_called_once()
                    mock_show.assert_called_once()
                    
                    # 验证调用顺序
                    all_calls = []
                    all_calls.extend([('cleanup', c) for c in mock_cleanup.call_args_list])
                    all_calls.extend([('create', c) for c in mock_create.call_args_list])
                    all_calls.extend([('show', c) for c in mock_show.call_args_list])
                    
                    # 按调用时间排序（这里简化为按添加顺序）
                    call_order = [call_type for call_type, _ in all_calls]
                    expected_order = ['cleanup', 'create', 'show']
                    
                    # 验证基本的调用存在性（顺序验证在实际环境中较复杂）
                    self.assertIn('cleanup', call_order)
                    self.assertIn('create', call_order)
                    self.assertIn('show', call_order)
        
        self.logger.info("✓ 窗口生命周期管理一致性测试通过")
    
    def test_function_signature_consistency(self):
        """测试函数签名的一致性"""
        self.logger.info("开始测试函数签名一致性")
        
        # 验证关键函数存在
        required_functions = [
            'preview_file',
            'preview_ppt', 
            'dropEvent',
            '_create_preview_window',
            '_cleanup_preview_window',
            '_show_preview_window',
            '_set_preview_window_title',
            'create_api_key_manager',
            'create_text_processor'
        ]
        
        for func_name in required_functions:
            self.assertTrue(hasattr(self.main_window, func_name), 
                          f"主窗口应该有 {func_name} 方法")
            self.assertTrue(callable(getattr(self.main_window, func_name)), 
                          f"{func_name} 应该是可调用的")
        
        # 验证preview_file函数签名
        import inspect
        preview_file_sig = inspect.signature(self.main_window.preview_file)
        self.assertEqual(len(preview_file_sig.parameters), 1, 
                        "preview_file应该接受1个参数（file_path）")
        
        param_name = list(preview_file_sig.parameters.keys())[0]
        self.assertEqual(param_name, 'file_path', 
                        "preview_file的参数应该命名为file_path")
        
        self.logger.info("✓ 函数签名一致性测试通过")
    
    def _simulate_drag_drop(self):
        """模拟拖放操作"""
        # 创建模拟的拖放事件
        mock_event = Mock()
        mock_mime_data = Mock()
        mock_url = Mock()
        
        mock_url.toLocalFile.return_value = self.test_ppt_file
        mock_mime_data.hasUrls.return_value = True
        mock_mime_data.urls.return_value = [mock_url]
        mock_event.mimeData.return_value = mock_mime_data
        mock_event.acceptProposedAction = Mock()
        
        # 执行拖放
        self.main_window.dropEvent(mock_event)
    
    def _simulate_preview_button(self):
        """模拟预览修改按钮操作"""
        # 设置选择的文件
        with patch.object(self.main_window, 'get_selected_files', return_value=[self.test_ppt_file]):
            self.main_window.preview_ppt()
    
    def _simulate_preview_button_error(self):
        """模拟预览修改按钮错误情况"""
        # 设置不存在的文件
        non_existent_file = os.path.join(self.test_dir, "non_existent.pptx")
        with patch.object(self.main_window, 'get_selected_files', return_value=[non_existent_file]):
            self.main_window.preview_ppt()
    
    def _simulate_drag_drop_error(self):
        """模拟拖放错误情况"""
        # 创建模拟的拖放事件（不存在的文件）
        mock_event = Mock()
        mock_mime_data = Mock()
        mock_url = Mock()
        
        non_existent_file = os.path.join(self.test_dir, "non_existent.pptx")
        mock_url.toLocalFile.return_value = non_existent_file
        mock_mime_data.hasUrls.return_value = True
        mock_mime_data.urls.return_value = [mock_url]
        mock_event.mimeData.return_value = mock_mime_data
        mock_event.acceptProposedAction = Mock()
        
        # 执行拖放
        self.main_window.dropEvent(mock_event)
    
    def _simulate_drag_drop_selective(self):
        """模拟拖放selective文件"""
        # 创建模拟的拖放事件
        mock_event = Mock()
        mock_mime_data = Mock()
        mock_url = Mock()
        
        mock_url.toLocalFile.return_value = self.selective_ppt_file
        mock_mime_data.hasUrls.return_value = True
        mock_mime_data.urls.return_value = [mock_url]
        mock_event.mimeData.return_value = mock_mime_data
        mock_event.acceptProposedAction = Mock()
        
        # 执行拖放
        self.main_window.dropEvent(mock_event)


def run_consistency_tests():
    """运行一致性测试"""
    print("=" * 80)
    print("开始执行预览功能一致性测试")
    print("=" * 80)
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(ConsistencyTestCase)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print("\n" + "=" * 80)
    print("一致性测试结果摘要")
    print("=" * 80)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 返回测试是否全部通过
    return len(result.failures) == 0 and len(result.errors) == 0


if __name__ == "__main__":
    success = run_consistency_tests()
    sys.exit(0 if success else 1)