"""
API密钥管理和请求缓存模块
提供高效的API密钥轮换和请求结果缓存
"""

import logging
import os
import threading
import time
from typing import Dict, List, Optional, Any, Tuple
import json
from pathlib import Path
from tenacity import retry, stop_after_attempt, wait_exponential

# 尝试导入diskcache，如果不存在则使用内存缓存
try:
    import diskcache as dc
    HAS_DISKCACHE = True
except ImportError:
    HAS_DISKCACHE = False


def load_api_keys(file_path: str) -> List[str]:
    """
    从文件加载API密钥列表
    
    Args:
        file_path: API密钥文件路径
        
    Returns:
        包含所有API密钥的列表
    """
    api_keys = []
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line in file:
                key = line.strip()
                if key:
                    api_keys.append(key)
    except FileNotFoundError:
        logging.error(f"API key file not found: {file_path}")
    return api_keys


class CacheManager:
    """
    管理API请求的缓存，支持持久化存储
    """
    
    def __init__(self, cache_dir: str = ".api_cache"):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录路径
        """
        self.cache_dir = cache_dir
        self._lock = threading.Lock()
        
        # 使用diskcache如果可用，否则回退到内存缓存
        if HAS_DISKCACHE:
            os.makedirs(cache_dir, exist_ok=True)
            self._cache = dc.Cache(cache_dir)
            logging.info(f"使用磁盘缓存: {cache_dir}")
        else:
            self._memory_cache: Dict[str, Tuple[Any, float]] = {}
            logging.info("使用内存缓存 (建议安装 diskcache 包以获得持久化缓存)")
    
    def get(self, key: str) -> Optional[Any]:
        """
        从缓存获取值
        
        Args:
            key: 缓存键名
            
        Returns:
            缓存的值，如果不存在则返回None
        """
        with self._lock:
            if HAS_DISKCACHE:
                return self._cache.get(key)
            else:
                item = self._memory_cache.get(key)
                if item is None:
                    return None
                    
                value, expire_time = item
                if expire_time == 0 or expire_time > time.time():
                    return value
                # 已过期
                del self._memory_cache[key]
                return None
    
    def set(self, key: str, value: Any, expire: int = 86400) -> None:
        """
        将值存入缓存
        
        Args:
            key: 缓存键名
            value: 要缓存的值
            expire: 过期时间(秒)，默认1天，0表示永不过期
        """
        with self._lock:
            if HAS_DISKCACHE:
                self._cache.set(key, value, expire=expire)
            else:
                expire_time = 0 if expire == 0 else time.time() + expire
                self._memory_cache[key] = (value, expire_time)
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            if HAS_DISKCACHE:
                self._cache.clear()
            else:
                self._memory_cache.clear()


class APIKeyManager:
    """
    管理API密钥的使用、轮换和限流
    """
    
    def __init__(self, api_keys_file: str):
        """
        初始化API密钥管理器
        
        Args:
            api_keys_file: API密钥文件路径
        """
        self.api_keys = load_api_keys(api_keys_file)
        self.last_used_time: Dict[str, float] = {key: 0 for key in self.api_keys}
        self.lock = threading.Lock()
        self.cache = CacheManager()
        
        # 添加API使用统计
        self.usage_stats: Dict[str, int] = {key: 0 for key in self.api_keys}
        
        # 加载已有统计数据
        self._load_stats()
        
        logging.info(f"已加载 {len(self.api_keys)} 个API密钥")
    
    def _load_stats(self) -> None:
        """加载API使用统计数据"""
        stats_file = Path(".api_stats.json")
        if stats_file.exists():
            try:
                with open(stats_file, "r", encoding="utf-8") as f:
                    stats = json.load(f)
                    # 只加载现有的密钥统计
                    for key in self.api_keys:
                        if key in stats:
                            self.usage_stats[key] = stats[key]
                logging.info("已加载API使用统计数据")
            except Exception as e:
                logging.error(f"加载API统计数据失败: {str(e)}")
    
    def _save_stats(self) -> None:
        """保存API使用统计数据"""
        try:
            with open(".api_stats.json", "w", encoding="utf-8") as f:
                json.dump(self.usage_stats, f)
        except Exception as e:
            logging.error(f"保存API统计数据失败: {str(e)}")
    
    @retry(stop=stop_after_attempt(5), wait=wait_exponential(multiplier=1, min=1, max=10))
    def get_next_key(self) -> Optional[str]:
        """
        获取下一个可用的API密钥，使用指数退避策略
        
        Returns:
            可用的API密钥，如果没有则返回None
        """
        with self.lock:
            if not self.api_keys:
                return None

            current_time = time.time()
            # 找到最长时间未使用且用量最少的API密钥
            available_keys = sorted(
                [(key, self.last_used_time[key], self.usage_stats.get(key, 0)) 
                 for key in self.api_keys],
                key=lambda x: (current_time - x[1], x[2])  # 优先考虑休息时间，其次是使用次数
            )

            for key, last_used, _ in available_keys:
                # 检查API密钥是否已经冷却足够时间（至少1秒）
                if current_time - last_used >= 1:
                    self.last_used_time[key] = current_time
                    self.usage_stats[key] = self.usage_stats.get(key, 0) + 1
                    
                    # 每10次调用保存一次统计
                    if self.usage_stats[key] % 10 == 0:
                        self._save_stats()
                        
                    return key

            # 如果没有立即可用的密钥，等待最快可用的密钥
            if available_keys:
                first_key, first_used, _ = available_keys[0]
                sleep_time = 1 - (current_time - first_used)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                self.last_used_time[first_key] = time.time()
                self.usage_stats[first_key] = self.usage_stats.get(first_key, 0) + 1
                return first_key

            return None
    
    def get_cached_text(self, text: str) -> Optional[str]:
        """
        从缓存中获取已处理过的文本
        
        Args:
            text: 原始文本
            
        Returns:
            缓存的处理结果，如果不存在则返回None
        """
        return self.cache.get(text)
    
    def cache_text(self, original_text: str, polished_text: str, expire: int = 2592000) -> None:
        """
        将处理过的文本存入缓存
        
        Args:
            original_text: 原始文本
            polished_text: 处理后的文本
            expire: 过期时间(秒)，默认30天
        """
        self.cache.set(original_text, polished_text, expire=expire) 