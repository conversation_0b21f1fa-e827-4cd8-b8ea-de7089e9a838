import os
import re
import difflib
import logging
import shutil
from pathlib import Path
from pptx import Presentation

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

class PPTTextReplacer:
    """PPT文本替换工具，支持多种替换策略"""
    
    def __init__(self, ppt_path):
        """初始化替换器
        
        Args:
            ppt_path: PPT文件路径
        """
        self.ppt_path = ppt_path
        self.backup_path = None
        self.prs = None
        self.load_presentation()
    
    def load_presentation(self):
        """加载PPT文件"""
        try:
            # 首先创建备份
            self._create_backup()
            
            # 加载演示文稿
            self.prs = Presentation(self.ppt_path)
            logging.info(f"成功加载PPT文件: {self.ppt_path}")
            logging.info(f"PPT包含 {len(self.prs.slides)} 张幻灯片")
        except Exception as e:
            logging.error(f"无法加载PPT文件: {str(e)}")
            raise
    
    def _create_backup(self):
        """创建PPT文件的备份"""
        original_path = Path(self.ppt_path)
        backup_name = f"{original_path.stem}_backup{original_path.suffix}"
        backup_path = original_path.parent / backup_name
        
        try:
            shutil.copy2(self.ppt_path, backup_path)
            self.backup_path = str(backup_path)
            logging.info(f"创建备份: {self.backup_path}")
        except Exception as e:
            logging.error(f"创建备份失败: {str(e)}")
    
    def normalize_text(self, text):
        """标准化文本，去除多余空格"""
        if not text:
            return ""
        return re.sub(r'\s+', ' ', text).strip()
    
    def simplify_text(self, text):
        """简化文本，去除标点符号和空格，转为小写"""
        if not text:
            return ""
        text = re.sub(r'\s+', ' ', text).strip()
        text = re.sub(r'[,.;:!?，。；：！？]', '', text)
        return text.lower()
    
    def find_and_replace_text(self, old_text, new_text, similarity_threshold=0.7):
        """在PPT中查找并替换文本
        
        Args:
            old_text: 要查找的原文本
            new_text: 要替换的新文本
            similarity_threshold: 相似度阈值
            
        Returns:
            包含替换位置信息的字典列表
        """
        if not self.prs:
            logging.error("PPT文件未加载")
            return []
        
        # 标准化要查找的文本
        normalized_old = self.normalize_text(old_text)
        simplified_old = self.simplify_text(old_text)
        
        # 跟踪替换位置
        replacements = []
        
        logging.info(f"查找并替换文本:")
        logging.info(f"原文本: '{old_text}'")
        logging.info(f"新文本: '{new_text}'")
        
        # 遍历所有幻灯片
        for slide_idx, slide in enumerate(self.prs.slides):
            slide_num = slide_idx + 1
            
            # 遍历幻灯片中的所有形状
            for shape_idx, shape in enumerate(slide.shapes):
                if not hasattr(shape, 'text_frame'):
                    continue
                
                text_frame = shape.text_frame
                
                # 跳过空文本框
                if not text_frame.text.strip():
                    continue
                
                # 获取完整文本
                full_text = text_frame.text
                normalized_full = self.normalize_text(full_text)
                
                # 检查整个文本框是否匹配
                similarity = difflib.SequenceMatcher(None, normalized_old, normalized_full).ratio()
                contains_text = normalized_old in normalized_full
                
                # 策略1: 整个文本框匹配度高
                if similarity > similarity_threshold or contains_text:
                    logging.info(f"在幻灯片 {slide_num} 找到匹配文本框，相似度: {similarity:.2f}")
                    
                    # 如果是精确匹配，直接替换整个文本框内容
                    if normalized_old == normalized_full:
                        logging.info(f"精确匹配，替换整个文本框")
                        text_frame.text = new_text
                        replacements.append({
                            'slide': slide_num,
                            'shape': shape_idx,
                            'type': 'exact_match',
                            'similarity': 1.0
                        })
                        continue
                
                # 策略2: 段落级匹配
                paragraph_replaced = False
                
                for para_idx, para in enumerate(text_frame.paragraphs):
                    para_text = para.text
                    normalized_para = self.normalize_text(para_text)
                    
                    # 计算段落级别的相似度
                    para_similarity = difflib.SequenceMatcher(None, normalized_old, normalized_para).ratio()
                    para_contains = normalized_old in normalized_para
                    
                    # 如果段落匹配度高
                    if para_similarity > similarity_threshold or para_contains:
                        logging.info(f"在幻灯片 {slide_num} 找到匹配段落，相似度: {para_similarity:.2f}")
                        
                        # 如果是精确段落匹配
                        if normalized_old == normalized_para:
                            logging.info(f"精确段落匹配，替换整个段落")
                            
                            # 清空段落中的所有运行块
                            while len(para.runs) > 0:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            
                            # 添加新的运行块
                            para.add_run().text = new_text
                            
                            replacements.append({
                                'slide': slide_num,
                                'shape': shape_idx,
                                'paragraph': para_idx,
                                'type': 'exact_paragraph_match',
                                'similarity': 1.0
                            })
                            
                            paragraph_replaced = True
                            break
                        
                        # 子字符串替换（如果是包含关系）
                        elif para_contains and not paragraph_replaced:
                            logging.info(f"部分段落匹配，替换部分文本")
                            
                            # 使用更精细的替换方法
                            new_para_text = re.sub(re.escape(normalized_old), new_text, normalized_para)
                            
                            # 清空段落中的所有运行块
                            while len(para.runs) > 0:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            
                            # 添加新的运行块
                            para.add_run().text = new_para_text
                            
                            replacements.append({
                                'slide': slide_num,
                                'shape': shape_idx,
                                'paragraph': para_idx,
                                'type': 'substring_match',
                                'similarity': para_similarity
                            })
                            
                            paragraph_replaced = True
                            break
                    
                    # 策略3: 运行块级别匹配
                    run_replaced = False
                    
                    for run_idx, run in enumerate(para.runs):
                        run_text = run.text
                        normalized_run = self.normalize_text(run_text)
                        
                        # 跳过空或太短的运行块
                        if len(normalized_run) < 10:
                            continue
                        
                        # 计算运行块级别的相似度
                        run_similarity = difflib.SequenceMatcher(None, normalized_old, normalized_run).ratio()
                        run_contains = normalized_old in normalized_run
                        
                        # 如果运行块匹配度高
                        if run_similarity > similarity_threshold or run_contains:
                            logging.info(f"在幻灯片 {slide_num} 找到匹配运行块，相似度: {run_similarity:.2f}")
                            
                            # 如果是精确运行块匹配
                            if normalized_old == normalized_run:
                                logging.info(f"精确运行块匹配，替换整个运行块")
                                run.text = new_text
                                
                                replacements.append({
                                    'slide': slide_num,
                                    'shape': shape_idx,
                                    'paragraph': para_idx,
                                    'run': run_idx,
                                    'type': 'exact_run_match',
                                    'similarity': 1.0
                                })
                                
                                run_replaced = True
                                break
                            
                            # 子字符串替换（如果是包含关系）
                            elif run_contains and not run_replaced:
                                logging.info(f"部分运行块匹配，替换部分文本")
                                
                                # 使用更精细的替换方法
                                run.text = re.sub(re.escape(normalized_old), new_text, normalized_run)
                                
                                replacements.append({
                                    'slide': slide_num,
                                    'shape': shape_idx,
                                    'paragraph': para_idx,
                                    'run': run_idx,
                                    'type': 'substring_run_match',
                                    'similarity': run_similarity
                                })
                                
                                run_replaced = True
                                break
                    
                    if run_replaced:
                        paragraph_replaced = True
                        break
                
                if paragraph_replaced:
                    continue
        
        # 保存修改
        self.save()
        
        logging.info(f"完成替换，共替换 {len(replacements)} 处")
        return replacements
    
    def replace_text_fuzzy(self, old_text, new_text, threshold=0.6):
        """使用模糊匹配替换文本
        
        Args:
            old_text: 要查找的原文本
            new_text: 要替换的新文本
            threshold: 相似度阈值
            
        Returns:
            替换的数量
        """
        replacements = self.find_and_replace_text(old_text, new_text, threshold)
        return len(replacements)
    
    def replace_text_with_com(self, old_text, new_text):
        """使用Windows COM接口替换文本（仅限Windows平台）
        
        Args:
            old_text: 要查找的原文本
            new_text: 要替换的新文本
            
        Returns:
            是否成功
        """
        try:
            import win32com.client
            import pythoncom
            
            logging.info("使用Windows COM接口替换文本...")
            
            # 初始化COM接口
            pythoncom.CoInitialize()
            
            # 创建PowerPoint应用程序实例
            ppt_app = win32com.client.Dispatch("PowerPoint.Application")
            ppt_app.Visible = False  # 后台运行
            
            try:
                # 打开演示文稿
                abs_path = os.path.abspath(self.ppt_path)
                presentation = ppt_app.Presentations.Open(abs_path, WithWindow=False)
                
                # 遍历所有幻灯片
                replacements = 0
                
                for slide_idx in range(1, presentation.Slides.Count + 1):
                    slide = presentation.Slides.Item(slide_idx)
                    
                    # 遍历所有形状
                    for shape_idx in range(1, slide.Shapes.Count + 1):
                        shape = slide.Shapes.Item(shape_idx)
                        
                        # 检查形状是否有文本框
                        if hasattr(shape, 'HasTextFrame') and shape.HasTextFrame:
                            text_frame = shape.TextFrame
                            
                            # 检查文本框是否有文本
                            if hasattr(text_frame, 'HasText') and text_frame.HasText:
                                # 使用PowerPoint内置的查找替换功能
                                old_count = len(text_frame.TextRange.Text)
                                text_frame.TextRange.Replace(
                                    FindWhat=old_text,
                                    ReplaceWhat=new_text,
                                    MatchCase=False,
                                    WholeWords=False
                                )
                                new_count = len(text_frame.TextRange.Text)
                                
                                # 判断是否替换成功
                                if old_count != new_count:
                                    replacements += 1
                
                # 保存并关闭
                presentation.Save()
                presentation.Close()
                
                logging.info(f"使用COM接口完成替换，共替换 {replacements} 处")
                return replacements > 0
                
            finally:
                # 退出PowerPoint
                ppt_app.Quit()
                pythoncom.CoUninitialize()
                
        except ImportError:
            logging.error("未安装win32com模块，无法使用COM接口替换")
            return False
        except Exception as e:
            logging.error(f"使用COM接口替换失败: {str(e)}")
            return False
    
    def save(self, output_path=None):
        """保存修改后的PPT
        
        Args:
            output_path: 可选的输出路径，如果不提供则覆盖原文件
        """
        if not self.prs:
            logging.error("PPT文件未加载")
            return False
        
        try:
            # 如果未提供输出路径，使用原路径
            save_path = output_path or self.ppt_path
            
            # 保存文件
            self.prs.save(save_path)
            logging.info(f"已保存修改后的PPT: {save_path}")
            return True
        except Exception as e:
            logging.error(f"保存PPT失败: {str(e)}")
            return False
    
    def restore_backup(self):
        """恢复备份文件"""
        if not self.backup_path or not os.path.exists(self.backup_path):
            logging.error("备份文件不存在")
            return False
        
        try:
            shutil.copy2(self.backup_path, self.ppt_path)
            logging.info(f"已恢复备份: {self.backup_path} -> {self.ppt_path}")
            return True
        except Exception as e:
            logging.error(f"恢复备份失败: {str(e)}")
            return False

def main():
    """主函数"""
    print("PPT文本替换工具")
    
    # 获取PPT文件路径
    ppt_path = input("请输入PPT文件路径: ").strip()
    
    if not os.path.exists(ppt_path):
        print(f"错误: 文件不存在 - {ppt_path}")
        return
    
    # 初始化替换器
    replacer = PPTTextReplacer(ppt_path)
    
    # 获取要替换的文本
    old_text = input("请输入要查找的文本: ").strip()
    new_text = input("请输入要替换的新文本: ").strip()
    
    # 选择替换方法
    print("\n选择替换方法:")
    print("1. 标准替换 (精确匹配)")
    print("2. 模糊替换 (相似度匹配)")
    print("3. COM接口替换 (仅Windows平台)")
    
    method = input("请选择替换方法 [1-3]: ").strip()
    
    # 执行替换
    if method == '1':
        replacer.find_and_replace_text(old_text, new_text, 0.9)
    elif method == '2':
        threshold = float(input("请输入相似度阈值 (0.0-1.0): ").strip() or "0.6")
        replacer.replace_text_fuzzy(old_text, new_text, threshold)
    elif method == '3':
        if replacer.replace_text_with_com(old_text, new_text):
            print("使用COM接口替换成功")
        else:
            print("使用COM接口替换失败，尝试标准替换方法...")
            replacer.find_and_replace_text(old_text, new_text, 0.8)
    else:
        print("无效的选择，使用标准替换方法")
        replacer.find_and_replace_text(old_text, new_text, 0.8)
    
    print("\n替换完成。详细结果请查看日志输出。")
    
    # 询问是否恢复备份
    restore = input("\n是否恢复备份? (y/N): ").strip().lower()
    if restore == 'y':
        if replacer.restore_backup():
            print("已成功恢复备份")
        else:
            print("恢复备份失败")

if __name__ == "__main__":
    main() 