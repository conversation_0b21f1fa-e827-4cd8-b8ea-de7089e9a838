"""
PPT预览窗口模块
提供PPT修改预览和对比功能
"""

import sys
import os
import tempfile
import logging
import json
import time
import math
import platform
import re
import shutil
import concurrent.futures
import win32com.client
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import weakref
import copy

# 设置Python日志
logger = logging.getLogger(__name__)

from PyQt5.QtCore import (
    Qt, QTimer, QObject, pyqtSignal, QThread, QEvent, QSize, QRectF, 
    QPoint, QPointF, QRect, QUrl, QMimeData, QBuffer, QByteArray, QIODevice,
    QMutex, QWaitCondition, QDateTime
)
from PyQt5.QtGui import (
    QPixmap, QImage, QPainter, QColor, QPen, QBrush, QFont, QPainterPath, 
    QCursor, QIcon, QKeySequence, QDrag, QPalette, QFontMetrics, QTransform
)
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGraphicsScene, 
    QGraphicsView, QPushButton, QLabel, QFrame, QScrollArea, QGraphicsRectItem, 
    QGraphicsTextItem, QGraphicsPixmapItem, QGraphicsItem, QSplitter, QListWidget, 
    QListWidgetItem, QMenu, QAction, QTextEdit, QSizePolicy, QStyle, QToolButton, 
    QMessageBox, QDialog, QFileDialog, QCheckBox, QSpinBox, QComboBox, QLineEdit,
    QSplashScreen, QTabWidget, QButtonGroup, QRadioButton, QGroupBox, QFormLayout,
    QGridLayout, QDockWidget, QToolBar, QProgressBar, QStyleOptionGraphicsItem,
    QTextBrowser, QStackedWidget, QSpacerItem, QAbstractItemView
)

from pptx import Presentation
from pptx.exc import PackageNotFoundError
from pptx.shapes.shapetree import GroupShapes, SlideShapes
from pptx.shapes.base import BaseShape

from ..processor.ppt_processor import PPTProcessor
from ..api.api_manager import APIKeyManager
from ..processor.text_processor import TextProcessor


class SlideRenderer(QThread):
    """幻灯片渲染线程，将幻灯片导出为图像"""
    
    slide_ready = pyqtSignal(int, str)  # 幻灯片索引, 图像路径
    finished = pyqtSignal()
    error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, prs_path: str, slide_indices: List[int], output_dir: str = None):
        super().__init__()
        self.prs_path = prs_path
        self.slide_indices = slide_indices
        self.stop_requested = False
        
        # 创建临时目录保存图像
        if output_dir:
            self.output_dir = output_dir
        else:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            self.output_dir = os.path.join(tempfile.gettempdir(), f"ppt_preview_{timestamp}")
        
        os.makedirs(self.output_dir, exist_ok=True)
    
    def run(self):
        try:
            # 确保pywin32和pythoncom可用
            try:
                import pythoncom
                pythoncom.CoInitialize()
            except (ImportError, AttributeError) as e:
                error_msg = f"缺少COM支持: {str(e)}，请确保安装了pywin32包"
                logging.error(error_msg)
                self.error.emit(error_msg)
                return
            
            # 创建PowerPoint应用实例
            try:
                powerpoint = win32com.client.Dispatch("PowerPoint.Application")
                powerpoint.Visible = True  # 必须设为可见，否则可能会出错
            except Exception as e:
                error_msg = f"无法启动PowerPoint应用: {str(e)}，请确保已安装Microsoft PowerPoint"
                logging.error(error_msg)
                self.error.emit(error_msg)
                pythoncom.CoUninitialize()
                return
            
            # 打开演示文稿
            try:
                presentation = powerpoint.Presentations.Open(self.prs_path, WithWindow=True)
            except Exception as e:
                error_msg = f"无法打开PowerPoint文件: {str(e)}"
                logging.error(error_msg)
                self.error.emit(error_msg)
                powerpoint.Quit()
                pythoncom.CoUninitialize()
                return
            
            try:
                # 确保输出目录存在
                os.makedirs(self.output_dir, exist_ok=True)
                
                # 导出每个幻灯片为图像
                for slide_index in self.slide_indices:
                    if self.stop_requested:
                        break
                    
                    # 幻灯片索引从1开始
                    # 导出为图像
                    img_path = os.path.join(self.output_dir, f"slide_{slide_index}.png")
                    
                    try:
                        # 导出当前幻灯片
                        presentation.Slides(slide_index).Export(img_path, "PNG")
                        time.sleep(0.1)  # 短暂延迟确保导出完成
                        
                        # 发出信号通知图像已准备好
                        self.slide_ready.emit(slide_index, img_path)
                    except Exception as e:
                        logging.error(f"导出幻灯片 {slide_index} 时出错: {str(e)}")
                        # 继续处理其他幻灯片
                
            finally:
                # 关闭演示文稿和PowerPoint
                try:
                    presentation.Close()
                    powerpoint.Quit()
                    
                    # 确保PowerPoint完全关闭
                    time.sleep(0.5)
                    
                    # 释放COM对象
                    del presentation
                    del powerpoint
                except:
                    pass
                
                # 关闭COM环境
                try:
                    pythoncom.CoUninitialize()
                except:
                    pass
            
            self.finished.emit()
            
        except Exception as e:
            error_msg = f"渲染幻灯片时出错: {str(e)}"
            logging.error(error_msg)
            self.error.emit(error_msg)
    
    def request_stop(self):
        """请求停止渲染"""
        self.stop_requested = True


class TextRegionItem(QGraphicsRectItem):
    """
    表示幻灯片上的一个可交互的文本区域
    """
    
    def __init__(self, x, y, width, height, region_id):
        """
        初始化文本区域

        Args:
            x: x坐标
            y: y坐标
            width: 宽度
            height: 高度
            region_id: 区域ID
        """
        super().__init__(x, y, width, height)
        self.region_id = region_id
        self.setAcceptHoverEvents(True)
        
        # 设置状态相关画刷
        self.default_brush = QBrush(QColor(0, 0, 0, 0))  # 完全透明
        self.hover_brush = QBrush(QColor(100, 200, 255, 80))  # 半透明蓝色
        self.rejected_brush = QBrush(QColor(255, 105, 180, 120))  # 半透明粉红色
        
        # 初始状态
        self.is_selected = False
        self.is_rejected = False
        self.setBrush(self.default_brush)
        
        # 设置为可选择
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        
        # 设置固定边框样式
        pen = QPen(QColor(255, 50, 50, 220), 3, Qt.DashLine)
        self.setPen(pen)
    
    def highlight(self, highlight=True):
        """高亮显示区域"""
        try:
            # 检查对象是否有效
            if not self.scene():
                return
                
            if highlight:
                self.is_selected = True
                # 如果被拒绝，则保持拒绝状态的画刷
                if not self.is_rejected:
                    self.setBrush(self.hover_brush)
            else:
                self.is_selected = False
                # 如果被拒绝，则保持拒绝状态的画刷
                if self.is_rejected:
                    self.setBrush(self.rejected_brush)
                else:
                    self.setBrush(self.default_brush)
                
                # 重置边框
                pen = self.pen()
                pen.setWidth(3)
                pen.setColor(QColor(255, 50, 50, 220))
                self.setPen(pen)
        except RuntimeError:
            # 对象可能已被删除
            logging.debug("TextRegionItem已被删除，无法高亮")
            return
    
    def set_rejected(self, rejected=True):
        """设置拒绝状态"""
        self.is_rejected = rejected
        if rejected:
            self.setBrush(self.rejected_brush)
        else:
            self.setBrush(self.default_brush)
    
    def hoverEnterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_selected and not self.is_rejected:
            self.setBrush(self.hover_brush)
            # 使边框更明显
            pen = self.pen()
            pen.setWidth(4)
            pen.setColor(QColor(0, 120, 215, 230))  # Windows蓝色
            self.setPen(pen)
        super().hoverEnterEvent(event)
    
    def hoverLeaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_selected:
            if self.is_rejected:
                self.setBrush(self.rejected_brush)
            else:
                self.setBrush(self.default_brush)
            # 恢复边框
            pen = self.pen()
            pen.setWidth(3)
            pen.setColor(QColor(255, 50, 50, 220))
            self.setPen(pen)
        super().hoverLeaveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        # 切换拒绝状态
        self.is_rejected = not self.is_rejected
        if self.is_rejected:
            self.setBrush(self.rejected_brush)
        else:
            self.setBrush(self.default_brush)
        
        # 发送点击信号及拒绝状态
        scene = self.scene()
        if scene:
            views = scene.views()
            if views:
                view = views[0]
                # 先发送点击信号
                if hasattr(view, 'handle_region_click'):
                    view.handle_region_click(self.region_id)
                # 也可以直接发送拒绝信号，确保状态更新
                if hasattr(view, 'textRegionRejected'):
                    try:
                        view.textRegionRejected.emit(self.region_id, self.is_rejected)
                    except Exception as e:
                        logging.error(f"发送拒绝信号时出错: {str(e)}")
        
        # 默认处理
        super().mousePressEvent(event)


class SlideViewer(QGraphicsView):
    """幻灯片查看器组件，使用QGraphicsView实现交互功能"""
    
    # 定义信号
    textRegionHovered = pyqtSignal(str)  # 传递change_id
    textRegionExited = pyqtSignal()
    textRegionClicked = pyqtSignal(str)  # 传递change_id
    textRegionRejected = pyqtSignal(str, bool)  # 传递change_id和拒绝状态
    
    def __init__(self, parent=None):
        """
        初始化幻灯片查看器组件
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 设置场景和视图属性
        self.scene = QGraphicsScene(self)
        self.setScene(self.scene)
        self.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        self.setRenderHint(QPainter.SmoothPixmapTransform)  # 平滑图像变换
        self.setDragMode(QGraphicsView.ScrollHandDrag)  # 拖动模式
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        
        # 设置尺寸策略和最小尺寸
        self.setMinimumSize(400, 300)
        size_policy = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setSizePolicy(size_policy)
        
        # 初始化数据属性
        self.images = []  # 幻灯片图像列表
        self.text_changes = {}  # 文本修改数据
        self.current_slide = 0  # 当前显示的幻灯片索引
        self.is_original = False  # 是否为原始幻灯片
        self.pixmap_item = None  # 用于显示幻灯片的QGraphicsPixmapItem
        
        # 创建标题标签
        self.title_label = QLabel()
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        
        # 创建布局
        self.layout = QVBoxLayout()
        self.layout.addWidget(self.title_label)
        
        # 初始化缩放因子
        self.zoom_factor = 1.0
        
        # 启用鼠标滚轮缩放
        self.setWheelScrollZoom(True)
        
        # 保存文本区域项的列表，用于后续操作
        self.text_region_items = {}
        
        # 添加定时器管理列表
        self.highlight_timers = []
    
    def set_title(self, title: str):
        """设置标题"""
        self.title_label.setText(title)
    
    def display_slide(self, slide_index, is_original=False):
        """
        显示幻灯片
        
        Args:
            slide_index: 幻灯片索引或图像路径
            is_original: 是否为原始幻灯片
        """
        # 清除所有定时器
        self._clear_highlight_timers()
        
        self.is_original = is_original
        
        # 保存当前缩放因子
        current_zoom = self.zoom_factor
        
        # 清除场景中的所有元素
        self.scene.clear()
        self.pixmap_item = None
        self.text_region_items.clear()
        
        # 判断参数类型
        if isinstance(slide_index, str):
            # 如果参数是字符串，则认为是图像路径
            image_path = slide_index
            # 存储当前处理的幻灯片索引（如果能从文件名中提取）
            try:
                # 尝试从文件名中提取幻灯片索引，格式通常为 "slide_X.png"
                filename = os.path.basename(image_path)
                if filename.startswith("slide_") and filename.endswith(".png"):
                    self.current_slide = int(filename[6:-4])  # 提取数字部分
                else:
                    self.current_slide = 0
            except:
                self.current_slide = 0
                
            # 设置标题
            title = f"{'原始' if is_original else '修改后'} 幻灯片"
            if self.current_slide > 0:
                title += f" #{self.current_slide}"
            self.set_title(title)
            
            # 加载图像并显示
            pixmap = self._image_to_pixmap(image_path)
            if pixmap:
                # 添加图像到场景
                self.pixmap_item = QGraphicsPixmapItem(pixmap)
                self.scene.addItem(self.pixmap_item)
                
                # 设置场景大小与图像一致
                self.scene.setSceneRect(0, 0, pixmap.width(), pixmap.height())
                
                # 仅在首次加载或缩放因子为1.0时调整视图适应图像
                if current_zoom == 1.0:
                    self.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
                else:
                    # 保持当前缩放级别
                    self.setTransform(QTransform().scale(current_zoom, current_zoom))
                
                # 如果是修改后的幻灯片，添加文本区域
                if not is_original and hasattr(self, 'text_changes') and self.text_changes:
                    # 查找当前幻灯片的修改
                    if self.current_slide in self.text_changes:
                        self.add_text_regions(self.text_changes[self.current_slide])
                        logging.debug(f"通过图像路径添加文本区域: 幻灯片 {self.current_slide}")
            else:
                self.show_message(f"无法加载图像: {image_path}")
        else:
            # 如果参数是数字，则认为是幻灯片索引
            self.current_slide = slide_index
            
            # 设置标题
            slide_number = slide_index + 1 if isinstance(slide_index, int) else "?"
            title = f"{'原始' if is_original else '修改后'} 幻灯片 #{slide_number}"
            self.set_title(title)
            
            # 检查幻灯片索引是否有效
            if slide_index < 0 or slide_index >= len(self.images):
                self.show_message("没有幻灯片可显示")
                return
            
            # 获取幻灯片图像
            image = self.images[slide_index]
            if image is None:
                self.show_message("加载幻灯片图像失败")
                return
            
            # 将图像转换为QPixmap并添加到场景
            pixmap = self._image_to_pixmap(image)
            if pixmap:
                # 添加图像到场景
                self.pixmap_item = QGraphicsPixmapItem(pixmap)
                self.scene.addItem(self.pixmap_item)
                
                # 设置场景大小与图像一致
                self.scene.setSceneRect(0, 0, pixmap.width(), pixmap.height())
                
                # 仅在首次加载或缩放因子为1.0时调整视图适应图像
                if current_zoom == 1.0:
                    self.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
                else:
                    # 保持当前缩放级别
                    self.setTransform(QTransform().scale(current_zoom, current_zoom))
                
                # 如果不是原始幻灯片，添加文本区域
                if not is_original and hasattr(self, 'text_changes') and self.text_changes and slide_index in self.text_changes:
                    self.add_text_regions(self.text_changes[slide_index])
                    
                    # 调试信息
                    logging.debug(f"幻灯片 {slide_index+1} 添加了 {len(self.text_region_items)} 个文本区域")
            else:
                self.show_message("无法加载幻灯片图像")
        
        # 保持当前缩放级别而不是重置
        self.zoom_factor = current_zoom
    
    def _clear_highlight_timers(self):
        """清除所有高亮定时器"""
        # 停止并删除所有定时器
        for timer in self.highlight_timers:
            if timer.isActive():
                timer.stop()
        
        # 清空定时器列表
        self.highlight_timers.clear()
    
    def add_text_regions(self, text_changes):
        """
        为文本修改添加交互区域
        
        Args:
            text_changes: 文本修改列表或字典
        """
        # 获取图像的原始大小
        if not self.pixmap_item:
            logging.error("无法添加文本区域：pixmap_item不存在")
            return
            
        pixmap_width = self.pixmap_item.pixmap().width()
        pixmap_height = self.pixmap_item.pixmap().height()
        
        logging.debug(f"添加文本区域，pixmap尺寸: {pixmap_width}x{pixmap_height}")
        
        # 确定文本修改的格式
        changes = []
        if isinstance(text_changes, dict) and "changes" in text_changes:
            changes = text_changes["changes"]
            logging.debug(f"从字典中提取文本修改: {len(changes)}项")
        elif isinstance(text_changes, list):
            changes = text_changes
            logging.debug(f"直接使用文本修改列表: {len(changes)}项")
        else:
            logging.error(f"不支持的文本修改格式: {type(text_changes)}, 内容: {text_changes}")
            return
        
        # 创建文本区域项
        regions_count = 0
        for i, change in enumerate(changes):
            try:
                # 检查change的格式
                if not isinstance(change, dict):
                    logging.error(f"修改项不是字典格式: {type(change)}, 内容: {change}")
                    continue
                
                # 对于大文档，可能需要处理大量的修改，打印详细日志
                if i < 5 or i % 20 == 0:  # 只打印前5项和之后每20项
                    logging.debug(f"处理第{i+1}项修改: {change.keys()}")
                    
                # 提取文本内容以便调试
                original_text = ""
                modified_text = ""
                if "original" in change:
                    original_text = change["original"]
                elif "original_text" in change:
                    original_text = change["original_text"]
                    
                if "modified" in change:
                    modified_text = change["modified"]
                elif "modified_text" in change:
                    modified_text = change["modified_text"]
                    
                if not original_text and not modified_text:
                    logging.warning(f"修改项缺少文本内容: {change}")
                    
                change_id = f"{self.current_slide}_{i}"
                
                # 获取位置信息
                position = None
                if "position" in change and isinstance(change["position"], dict):
                    position = change["position"]
                    logging.debug(f"获取到位置信息: {position.keys() if position else None}")
                
                # 如果没有位置信息，使用默认位置
                if not position:
                    # 设置一个默认位置在幻灯片中部，但为每个修改项指定不同位置
                    # 修改为更明显的扇形分布
                    angle = (i * 30) % 360  # 每30度放置一个，形成环形分布
                    radius = min(pixmap_width, pixmap_height) * 0.3  # 使用较小尺寸的30%作为半径
                    center_x = pixmap_width * 0.5
                    center_y = pixmap_height * 0.5
                    
                    # 计算扇形分布的坐标
                    import math
                    rad = math.radians(angle)
                    x = center_x + radius * math.cos(rad) - 100  # 减去宽度的一半使中心对准点
                    y = center_y + radius * math.sin(rad) - 50   # 减去高度的一半使中心对准点
                    
                    width = pixmap_width * 0.25  # 增大默认宽度
                    height = pixmap_height * 0.15  # 增大默认高度
                    
                    logging.debug(f"使用默认位置: x={x}, y={y}, width={width}, height={height}")
                else:
                    try:
                        # 获取幻灯片真实尺寸信息（如果存在）
                        slide_width = position.get("slide_width", 9144000)  # PowerPoint默认EMU单位
                        slide_height = position.get("slide_height", 6858000)  # 默认4:3比例

                        # 使用真实幻灯片尺寸进行坐标转换，提高准确度
                        scale_x = pixmap_width / slide_width
                        scale_y = pixmap_height / slide_height
                        
                        # 使用相对位置计算（如果可用）
                        if "relative_x" in position and "relative_y" in position:
                            x = position["relative_x"] * pixmap_width
                            y = position["relative_y"] * pixmap_height
                            width = position["relative_width"] * pixmap_width if "relative_width" in position else pixmap_width * 0.4
                            height = position["relative_height"] * pixmap_height if "relative_height" in position else pixmap_height * 0.1
                            logging.debug(f"使用相对位置: x={x}, y={y}, width={width}, height={height}")
                        else:
                            # 使用绝对位置
                            x = position["x"] * scale_x
                            y = position["y"] * scale_y
                            width = max(position["width"] * scale_x, pixmap_width * 0.2)  # 确保区域足够大
                            height = max(position["height"] * scale_y, pixmap_height * 0.05)  # 确保区域足够大
                            logging.debug(f"使用绝对位置: x={x}, y={y}, width={width}, height={height}")
                    except Exception as e:
                        logging.error(f"处理位置信息时出错: {str(e)}")
                        # 使用默认位置作为备选
                        x = pixmap_width * 0.1 + (i * 30) % (pixmap_width * 0.8)
                        y = pixmap_height * 0.1 + (i * 30) % (pixmap_height * 0.8)
                        width = pixmap_width * 0.25
                        height = pixmap_height * 0.15
                
                # 确保区域足够大，方便交互
                width = max(width, pixmap_width * 0.15)
                height = max(height, pixmap_height * 0.08)
                
                # 确保坐标在合法范围内
                x = max(0, min(x, pixmap_width - width))
                y = max(0, min(y, pixmap_height - height))
                
                # 创建文本区域项
                region = TextRegionItem(x, y, width, height, change_id)
                
                # 设置更明显的边框
                # 使用非常醒目的红色和更粗的线条
                region.setPen(QPen(QColor(255, 30, 30, 240), 3, Qt.DashLine))
                self.scene.addItem(region)
                self.text_region_items[change_id] = region
                regions_count += 1
                
                # 创建标签背景矩形 - 更大更明显
                label_size = 35
                label_bg = QGraphicsRectItem(x, y, label_size, label_size)
                label_bg.setBrush(QBrush(QColor(0, 120, 215, 250)))  # Windows 蓝
                label_bg.setPen(QPen(Qt.white, 1, Qt.SolidLine))
                self.scene.addItem(label_bg)
                
                # 添加区域编号标签
                label_text = QGraphicsTextItem(str(i + 1))
                label_text.setDefaultTextColor(QColor(255, 255, 255))
                label_text.setFont(QFont("Arial", 14, QFont.Bold))
                # 调整文本位置使其居中于背景矩形
                text_rect = label_text.boundingRect()
                label_text.setPos(x + (label_size - text_rect.width()) / 2, y + (label_size - text_rect.height()) / 2 - 1)
                self.scene.addItem(label_text)
                
                # 确保区域初始状态为透明
                region.setBrush(QBrush(QColor(0, 0, 0, 0)))
                
            except Exception as e:
                logging.error(f"创建文本区域时出错: {str(e)}")
                
        logging.info(f"成功添加 {regions_count}/{len(changes)} 个文本区域")
        
        # 如果没有添加任何区域但有修改项，记录警告
        if regions_count == 0 and changes:
            logging.warning(f"没有创建任何文本区域，但有 {len(changes)} 个修改项")
    
    def _safe_highlight(self, region_ref, highlight):
        """安全地高亮区域，确保对象仍然有效"""
        region = region_ref()
        if region and region.scene():
            try:
                region.highlight(highlight)
            except RuntimeError:
                # 对象可能已被删除
                pass
    
    def highlight_region(self, change_id):
        """
        高亮显示指定ID的文本区域
        
        Args:
            change_id: 文本修改ID，如果为None则清除所有高亮
        """
        # 首先清除所有高亮
        for region in self.text_region_items.values():
            region.setBrush(QBrush(QColor(0, 0, 0, 0)))
        
        # 如果提供了change_id，高亮指定区域
        if change_id is not None and change_id in self.text_region_items:
            self.text_region_items[change_id].setBrush(QBrush(QColor(47, 155, 255, 80)))  # 半透明蓝色
            self.ensureVisible(self.text_region_items[change_id])  # 确保区域可见
    
    def show_message(self, message: str):
        """显示消息"""
        # 清除场景
        self.scene.clear()
        self.text_region_items.clear()
        
        # 添加文本信息
        text = self.scene.addText(message)
        text.setDefaultTextColor(QColor(255, 255, 255))
        text.setFont(QFont("Microsoft YaHei", 12))
        
        # 调整文本位置到中心
        text_rect = text.boundingRect()
        text.setPos((self.width() - text_rect.width()) / 2, (self.height() - text_rect.height()) / 2)
    
    def resizeEvent(self, event):
        """处理视图大小变化"""
        super().resizeEvent(event)
        # 延迟执行缩放，避免过早调用可能导致的问题
        QApplication.processEvents()
        # 只有在有图像时才进行缩放适应
        if self.pixmap_item and self.scene.sceneRect().isValid():
            # 保存当前视图中心点
            center_point = self.mapToScene(self.viewport().rect().center())
            
            # 使用合适的方式适应视图大小
            self.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
            
            # 更新文本区域位置（如果需要）
            self._update_text_regions_after_transform()
            
            # 恢复视图到原来的中心点
            self.centerOn(center_point)
    
    def _update_text_regions_after_transform(self):
        """在视图变换后更新文本区域的位置"""
        if not self.pixmap_item:
            return
        
        # 仅当存在文本区域时才进行更新
        if not self.text_region_items:
            return
            
        # 获取当前变换矩阵
        transform = self.transform()
        
        # 更新标签位置以适应新的视图
        for item in self.scene.items():
            if isinstance(item, QGraphicsTextItem):
                # 找到相应的背景矩形并调整
                for bg_item in self.scene.items():
                    if isinstance(bg_item, QGraphicsRectItem) and bg_item.pos() == item.pos():
                        # 重新调整文本位置
                        text_rect = item.boundingRect()
                        bg_rect = bg_item.rect()
                        item.setPos(bg_item.pos().x() + (bg_rect.width() - text_rect.width()) / 2, 
                                    bg_item.pos().y() + (bg_rect.height() - text_rect.height()) / 2)
                        break
    
    def setZoomFactor(self, factor):
        """
        设置视图缩放系数
        
        Args:
            factor: 缩放系数
        """
        self.zoom_factor = factor
        self.setTransform(QTransform().scale(factor, factor))
    
    def setWheelScrollZoom(self, enabled):
        """
        设置是否允许滚轮缩放
        
        Args:
            enabled: 是否启用
        """
        self.wheel_zoom_enabled = enabled
    
    def wheelEvent(self, event):
        """
        处理滚轮事件
        
        Args:
            event: 滚轮事件
        """
        if self.wheel_zoom_enabled and event.modifiers() == Qt.ControlModifier:
            # 使用Ctrl+滚轮进行缩放
            delta = event.angleDelta().y()
            if delta > 0:
                # 放大
                factor = 1.1
            else:
                # 缩小
                factor = 0.9
            
            # 更新缩放因子
            self.zoom_factor *= factor
            
            # 限制缩放范围
            self.zoom_factor = max(0.1, min(self.zoom_factor, 10.0))
            
            # 应用变换
            self.setTransform(QTransform().scale(self.zoom_factor, self.zoom_factor))
            
            # 阻止事件传递
            event.accept()
        else:
            # 正常滚动
            super().wheelEvent(event)
    
    def handle_region_click(self, region_id):
        """
        处理文本区域点击事件
        
        Args:
            region_id: 被点击的区域ID，格式为 "slide_index_change_index"
        """
        logging.debug(f"文本区域被点击: {region_id}")
        
        # 获取区域的拒绝状态
        is_rejected = False
        if region_id in self.text_region_items:
            region = self.text_region_items[region_id]
            if hasattr(region, 'is_rejected'):
                is_rejected = region.is_rejected
        
        # 发出区域点击信号
        logging.debug(f"发出textRegionClicked信号: {region_id}")
        self.textRegionClicked.emit(region_id)
        
        # 发出区域拒绝信号
        logging.debug(f"发出textRegionRejected信号: {region_id}, 拒绝状态: {is_rejected}")
        self.textRegionRejected.emit(region_id, is_rejected)
    
    def _image_to_pixmap(self, image):
        """
        将图像转换为QPixmap
        
        Args:
            image: QImage或图像路径
            
        Returns:
            QPixmap对象或None
        """
        try:
            # 如果传入的是字符串路径
            if isinstance(image, str):
                if os.path.exists(image):
                    return QPixmap(image)
                else:
                    logging.error(f"图像文件不存在: {image}")
                    return None
            # 如果传入的是QImage
            elif isinstance(image, QImage):
                return QPixmap.fromImage(image)
            # 如果传入的是QPixmap
            elif isinstance(image, QPixmap):
                return image
            else:
                logging.error(f"不支持的图像类型: {type(image)}")
                return None
        except Exception as e:
            logging.error(f"转换图像时出错: {str(e)}")
            return None


class TextChangeItem(QFrame):
    """表示单个文本修改的组件"""
    
    decision_changed = pyqtSignal(object, bool)  # 修改项, 是否接受
    hovered = pyqtSignal(str)  # 传递change_id
    exited = pyqtSignal()
    
    def __init__(self, change_id, original_text, modified_text, change_index=None, parent=None):
        super().__init__(parent)
        self.change_id = change_id
        self.change_index = change_index  # 修改在列表中的索引
        self.original_text = original_text
        self.modified_text = modified_text
        self.is_accepted = True  # 默认接受
        
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setStyleSheet("""
            TextChangeItem {
                border: 1px solid #3C3F41;
                border-radius: 3px;
                padding: 5px;
                background-color: #2C2F30;
            }
        """)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 修改索引标签（如果提供）
        if self.change_index is not None:
            index_layout = QHBoxLayout()
            index_label = QLabel(f"修改 #{self.change_index + 1}")
            index_label.setStyleSheet("font-weight: bold; color: #2196F3;")
            index_layout.addWidget(index_label)
            index_layout.addStretch()
            layout.addLayout(index_layout)
        
        # 原始文本
        original_layout = QVBoxLayout()
        original_label = QLabel("原始文本:")
        original_label.setStyleSheet("font-weight: bold;")
        original_layout.addWidget(original_label)
        
        original_text = QTextEdit()
        original_text.setReadOnly(True)
        original_text.setMaximumHeight(80)
        original_text.setText(self.original_text)
        original_layout.addWidget(original_text)
        
        layout.addLayout(original_layout)
        
        # 修改后文本
        modified_layout = QVBoxLayout()
        modified_label = QLabel("修改后文本:")
        modified_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        modified_layout.addWidget(modified_label)
        
        modified_text = QTextEdit()
        modified_text.setReadOnly(True)
        modified_text.setMaximumHeight(80)
        modified_text.setText(self.modified_text)
        modified_text.setStyleSheet("color: #4CAF50;")
        modified_layout.addWidget(modified_text)
        
        layout.addLayout(modified_layout)
        
        # 接受/拒绝按钮
        button_layout = QHBoxLayout()
        self.accept_button = QPushButton("接受此修改")
        self.accept_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: 1px solid #4CAF50;
            }
        """)
        self.accept_button.clicked.connect(self.accept_change)
        
        self.reject_button = QPushButton("拒绝此修改")
        self.reject_button.clicked.connect(self.reject_change)
        
        button_layout.addWidget(self.accept_button)
        button_layout.addWidget(self.reject_button)
        layout.addLayout(button_layout)
    
    def accept_change(self):
        """接受修改"""
        self.is_accepted = True
        self.update_button_state()
        self.decision_changed.emit(self, True)
    
    def reject_change(self):
        """拒绝修改"""
        self.is_accepted = False
        self.update_button_state()
        self.decision_changed.emit(self, False)
    
    def update_button_state(self):
        """更新按钮状态"""
        if self.is_accepted:
            self.accept_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    border: 1px solid #4CAF50;
                }
            """)
            self.reject_button.setStyleSheet("")
        else:
            self.reject_button.setStyleSheet("""
                QPushButton {
                    background-color: #F44336;
                    border: 1px solid #F44336;
                }
            """)
            self.accept_button.setStyleSheet("")
    
    def set_decision(self, is_accepted):
        """设置决策状态"""
        self.is_accepted = is_accepted
        self.update_button_state()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        # 发出悬停信号
        self.setStyleSheet("""
            TextChangeItem {
                border: 2px solid #2196F3;
                border-radius: 3px;
                padding: 5px;
                background-color: #1E2A38;
            }
        """)
        self.hovered.emit(self.change_id)
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        # 发出离开信号
        self.setStyleSheet("""
            TextChangeItem {
                border: 1px solid #3C3F41;
                border-radius: 3px;
                padding: 5px;
                background-color: #2C2F30;
            }
        """)
        self.exited.emit()
        super().leaveEvent(event)


class PreviewWindow(QMainWindow):
    """PPT预览窗口类，用于预览和比较原始PPT和修改版本"""
    
    # 定义修改完成信号
    preview_completed = pyqtSignal(list)
    
    def __init__(self, 
                original_ppt_path=None,
                modified_ppt_path=None,
                text_changes=None,
                slides_to_modify=None,
                api_key_manager=None,
                text_processor=None):
        """
        初始化预览窗口
        
        Args:
            original_ppt_path: 原始PPT文件路径
            modified_ppt_path: 修改后的PPT文件路径
            text_changes: 文本修改数据，格式为 {slide_idx: {'changes': [change_dict, ...]}}
            slides_to_modify: 要修改的幻灯片索引列表
            api_key_manager: API密钥管理器实例
            text_processor: 文本处理器实例
        """
        super().__init__()
        
        # 滚轮事件相关变量
        self.last_wheel_time = 0  # 上次滚轮事件的时间
        self.wheel_mode = None  # 当前滚轮模式：'zoom' 或 'navigation'
        self.wheel_speed_threshold = 120  # 判断为快速滚动的阈值
        self.wheel_time_threshold = 300  # 毫秒，模式切换的时间阈值
        
        # 初始化窗口基本属性
        self.setWindowTitle("PPT预览与选择")

        # 设置API密钥管理器和文本处理器
        self.api_key_manager = api_key_manager
        self.text_processor = text_processor
        
        # 初始化PPT文件相关属性
        self.original_ppt_path = original_ppt_path
        self.modified_ppt_path = modified_ppt_path
        self.text_changes = text_changes or {}
        
        # 初始化幻灯片相关属性
        self.original_slides = []  # 原始幻灯片图像列表
        self.modified_slides = []  # 修改后幻灯片图像列表
        self.slide_decisions = {}  # 幻灯片决策，格式为 {slide_index: {'accept': True}}
        self.change_decisions = {}  # 修改决策，格式为 {slide_index: {change_id: True}}
        
        # 添加用户选择属性，用于存储用户对修改项的选择
        self.user_choices = {}  # 格式为 {change_id: bool} 表示用户是否接受该修改
        
        # 初始化幻灯片浏览状态
        self.current_slide = 0
        self.total_slides = 0
        self.slides_to_modify = slides_to_modify or []  # 要修改的幻灯片索引列表
        self.all_slides = []  # 所有幻灯片索引列表
        
        # 获取所有幻灯片索引
        try:
            if original_ppt_path and os.path.exists(original_ppt_path):
                from pptx import Presentation
                prs = Presentation(original_ppt_path)
                self.all_slides = list(range(1, len(prs.slides) + 1))
                logging.info(f"PPT包含 {len(self.all_slides)} 张幻灯片")
        except Exception as e:
            logging.error(f"获取所有幻灯片索引失败: {str(e)}")
            self.all_slides = self.slides_to_modify.copy() if self.slides_to_modify else []
        
        # 标记哪些幻灯片有修改
        self.slides_with_changes = set(self.slides_to_modify)
        
        # 初始化渲染线程和互斥锁
        self.original_renderer = None
        self.modified_renderer = None
        self.rendering_mutex = QMutex()

        # 初始化UI
        self.init_ui()
        
        # 连接信号和槽
        self.connect_preview_signals()
        
        # 如果路径已提供，直接加载PPT
        if self.modified_ppt_path:
            self.load_ppt(self.modified_ppt_path)
    
    def load_ppt(self, ppt_file_path):
        """
        加载并预览PPT文件
        
        Args:
            ppt_file_path: 要预览的PPT文件路径
        """
        if not os.path.exists(ppt_file_path):
            QMessageBox.warning(self, "文件错误", f"文件不存在: {ppt_file_path}")
            return False
            
        # 设置加载状态
        self.statusBar().showMessage("正在准备预览...")
        QApplication.processEvents()
        
        try:
            # 创建临时目录
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            self.temp_dir = os.path.join(tempfile.gettempdir(), f"ppt_preview_{timestamp}")
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # 为原始和修改后的幻灯片创建子目录
            self.original_img_dir = os.path.join(self.temp_dir, "original")
            self.modified_img_dir = os.path.join(self.temp_dir, "modified")
            os.makedirs(self.original_img_dir, exist_ok=True)
            os.makedirs(self.modified_img_dir, exist_ok=True)
            
            # 清空之前的数据
            self.original_slides = []  # 使用列表而不是字典
            self.modified_slides = []  # 使用列表而不是字典
            self.change_decisions = {}
            
            # 清空幻灯片列表
            if hasattr(self, 'slide_list'):
                self.slide_list.clear()
            
            # 清空比较区域
            if hasattr(self, 'changes_layout'):
                while self.changes_layout.count() > 0:
                    item = self.changes_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            
            # 设置修改后的PPT文件路径（如果之前未设置）
            if not self.modified_ppt_path:
                self.modified_ppt_path = ppt_file_path
            
            # 从文件名推断原始文件路径（如果之前未设置）
            if not self.original_ppt_path:
                self.original_ppt_path = self.infer_original_path(self.modified_ppt_path)
            
            if not os.path.exists(self.original_ppt_path) or self.original_ppt_path == self.modified_ppt_path:
                # 创建一个对话框，提供用户选择
                message_box = QMessageBox(self)
                message_box.setWindowTitle("找不到原始文件")
                message_box.setIcon(QMessageBox.Question)
                message_box.setText(f"系统无法找到原始PPT文件，或推断出的原始文件与修改后文件相同。\n\n"
                                    f"检测到的修改后文件: {self.modified_ppt_path}\n"
                                    f"推断的原始文件: {self.original_ppt_path}\n\n"
                                    f"请选择如何处理:")
                
                # 添加按钮
                browse_btn = message_box.addButton("浏览选择原始文件", QMessageBox.ActionRole)
                use_same_btn = message_box.addButton("使用同一文件", QMessageBox.AcceptRole)
                cancel_btn = message_box.addButton("取消", QMessageBox.RejectRole)
                
                # 设置默认按钮
                message_box.setDefaultButton(browse_btn)
                
                # 显示对话框
                message_box.exec_()
                
                clicked_button = message_box.clickedButton()
                
                if clicked_button == browse_btn:
                    # 用户选择浏览原始文件
                    file_dialog = QFileDialog(self)
                    file_dialog.setWindowTitle("选择原始PPT文件")
                    file_dialog.setNameFilter("PowerPoint文件 (*.ppt *.pptx)")
                    file_dialog.setFileMode(QFileDialog.ExistingFile)
                    
                    if file_dialog.exec_():
                        selected_files = file_dialog.selectedFiles()
                        if selected_files:
                            self.original_ppt_path = selected_files[0]
                            logging.info(f"用户选择原始文件: {self.original_ppt_path}")
                        else:
                            logging.warning("用户取消选择原始文件")
                            return False
                    else:
                        logging.warning("用户取消选择原始文件")
                        return False
                
                elif clicked_button == use_same_btn:
                    # 用户确认使用同一文件
                    self.original_ppt_path = self.modified_ppt_path
                    logging.info(f"使用修改后文件作为原始文件: {self.original_ppt_path}")
                
                elif clicked_button == cancel_btn:
                    # 用户取消
                    logging.info("用户取消加载")
                    return False
            
            # 加载PPT
            modified_prs = Presentation(self.modified_ppt_path)
            
            # 确定要处理的幻灯片（如果之前未设置）
            if not self.slides_to_modify:
                self.slides_to_modify = list(range(1, len(modified_prs.slides) + 1))
            
            # 设置所有幻灯片索引（如果之前未设置）
            if not hasattr(self, 'all_slides') or not self.all_slides:
                self.all_slides = self.slides_to_modify.copy()
            
            # 初始化有修改的幻灯片集合（如果之前未设置）
            if not hasattr(self, 'slides_with_changes'):
                self.slides_with_changes = set()
            
            self.total_slides = len(self.slides_to_modify)
            
            # 分析文本变化（如果之前未设置）
            if not self.text_changes:
                self.analyze_text_changes()
                # 更新slides_with_changes集合
                self.slides_with_changes = set(self.text_changes.keys())
                logging.info(f"有修改的幻灯片索引: {self.slides_with_changes}")
            
            # 初始化幻灯片列表UI（延迟显示）
            self.initialize_slides_ui()
            
            # 开始渲染幻灯片
            self.start_slide_rendering()
            
            return True
            
        except Exception as e:
            QMessageBox.critical(self, "加载错误", f"加载PPT时发生错误: {str(e)}")
            logging.exception(f"加载PPT时发生错误: {str(e)}")
            return False
    
    def infer_original_path(self, modified_path):
        """
        根据修改后的文件路径推断原始文件路径
        
        Args:
            modified_path: 修改后的文件路径
            
        Returns:
            推断的原始文件路径
        """
        # 获取文件名和目录
        file_name = os.path.basename(modified_path)
        dir_path = os.path.dirname(modified_path)
        parent_dir = os.path.dirname(dir_path)
        
        # 记录推断过程
        logging.info(f"尝试为文件 {file_name} 推断原始文件路径")
        
        # 处理preview_开头的文件
        if file_name.startswith("preview_"):
            logging.info("检测到preview_格式文件，尝试提取原始文件名")
            
            # 按照固定格式提取: preview_原始文件名_YYYYMMDD_HHMMSS.pptx
            # 正则表达式提取原始文件名
            original_name_pattern = r'^preview_(.+?)_\d{8}_\d{6}\.(pptx?)'
            match = re.match(original_name_pattern, file_name)
            
            if match:
                # 直接从文件名中提取
                original_name = match.group(1) + "." + match.group(2)
                logging.info(f"从preview文件名提取的原始文件名: {original_name}")
                
                # 在同一目录中查找匹配的原始文件
                original_path = os.path.join(dir_path, original_name)
                if os.path.exists(original_path):
                    logging.info(f"在同一目录中找到原始文件: {original_path}")
                    return original_path
                
                # 如果没有找到，尝试检查原始文件可能在其他目录
                # 在parent_dir目录下查找
                parent_path = os.path.join(parent_dir, original_name)
                if os.path.exists(parent_path):
                    logging.info(f"在父目录中找到原始文件: {parent_path}")
                    return parent_path
                
                # 尝试在folder_xx目录中查找
                current_dir_name = os.path.basename(dir_path)
                if "-" in current_dir_name:
                    base_number = current_dir_name.split("-")[0]
                    potential_folders = [
                        os.path.join(parent_dir, f"folder_{base_number}"),
                        os.path.join(parent_dir, f"{base_number}"),
                        os.path.join(parent_dir, f"Folder_{base_number}")
                    ]
                    
                    for folder in potential_folders:
                        if os.path.exists(folder) and os.path.isdir(folder):
                            original_path = os.path.join(folder, original_name)
                            if os.path.exists(original_path):
                                logging.info(f"在folder_{base_number}目录中找到原始文件: {original_path}")
                                return original_path
            else:
                # 如果正则匹配失败，尝试更简单的切分方法
                name_without_ext, ext = os.path.splitext(file_name)
                
                # 去除preview_前缀
                if name_without_ext.startswith("preview_"):
                    name_without_prefix = name_without_ext[8:]  # 移除 "preview_"
                    
                    # 查找时间戳部分（_YYYYMMDD_HHMMSS）
                    timestamp_pattern = r'_\d{8}_\d{6}$'
                    match = re.search(timestamp_pattern, name_without_prefix)
                    
                    if match:
                        # 移除时间戳
                        original_name = name_without_prefix[:match.start()] + ext
                        logging.info(f"从preview文件名提取的原始文件名: {original_name}")
                        
                        # 在同一目录中查找
                        original_path = os.path.join(dir_path, original_name)
                        if os.path.exists(original_path):
                            logging.info(f"在同一目录中找到原始文件: {original_path}")
                            return original_path
                            
                        # 在父目录中查找
                        parent_path = os.path.join(parent_dir, original_name)
                        if os.path.exists(parent_path):
                            logging.info(f"在父目录中找到原始文件: {parent_path}")
                            return parent_path
                            
                        # 尝试在folder_xx目录中查找
                        current_dir_name = os.path.basename(dir_path)
                        if "-" in current_dir_name:
                            base_number = current_dir_name.split("-")[0]
                            folder_path = os.path.join(parent_dir, f"folder_{base_number}")
                            if os.path.exists(folder_path) and os.path.isdir(folder_path):
                                folder_original_path = os.path.join(folder_path, original_name)
                                if os.path.exists(folder_original_path):
                                    logging.info(f"在folder_{base_number}目录中找到原始文件: {folder_original_path}")
                                    return folder_original_path
        
        # 处理modified_开头的文件（原有逻辑）
        elif file_name.startswith("modified_"):
            # 提取原始文件名
            parts = file_name.split("_", 2)
            if len(parts) >= 3:
                original_file_name = parts[2]
                
                # 从目录名推断原始文件夹
                current_dir_name = os.path.basename(dir_path)
                
                # 如果目录名符合XX-1格式
                if "-" in current_dir_name:
                    base_number = current_dir_name.split("-")[0]
                    original_dir = os.path.join(parent_dir, f"folder_{base_number}")
                    
                    # 检查原始目录是否存在
                    if os.path.exists(original_dir) and os.path.isdir(original_dir):
                        # 在原始目录中查找原始文件
                        for file in os.listdir(original_dir):
                            if file.lower() == original_file_name.lower():
                                return os.path.join(original_dir, file)
        
        # 兜底方案：在当前目录下寻找非preview_和modified_开头的PPT文件
        ppt_files = []
        for file in os.listdir(dir_path):
            if (file.lower() != file_name.lower() and 
                file.lower().endswith(('.ppt', '.pptx')) and 
                not file.startswith(("preview_", "modified_"))):
                ppt_files.append(file)
        
        if ppt_files:
            # 如果找到多个可能的原始文件，选择与修改文件名最相似的
            if len(ppt_files) > 1:
                # 提取当前文件的主体名称（去除preview_和时间戳）
                current_name = file_name
                if file_name.startswith("preview_"):
                    current_name = self._extract_base_name(file_name)
                
                # 计算相似度并选择最相似的文件
                max_similarity = 0
                best_match = ppt_files[0]
                
                for ppt_file in ppt_files:
                    similarity = self._calculate_filename_similarity(ppt_file, current_name)
                    if similarity > max_similarity:
                        max_similarity = similarity
                        best_match = ppt_file
                
                original_path = os.path.join(dir_path, best_match)
                logging.info(f"在同目录找到最匹配的文件: {original_path} (相似度: {max_similarity})")
                return original_path
            else:
                # 只找到一个可能的原始文件
                original_path = os.path.join(dir_path, ppt_files[0])
                logging.info(f"在同目录找到唯一的可能原始文件: {original_path}")
                return original_path
                
        # 如果依然无法找到合适的文件，返回修改后的文件路径
        logging.warning(f"无法找到原始文件，使用修改后文件作为原始文件: {modified_path}")
        return modified_path
        
    def _extract_base_name(self, filename):
        """从文件名中提取基础名称，去除prefix_和时间戳"""
        name, ext = os.path.splitext(filename)
        
        # 移除前缀
        if name.startswith("preview_") or name.startswith("modified_"):
            name = name.split("_", 1)[1]
        
        # 移除可能的时间戳（格式通常为 YYYYMMDD_HHMMSS）
        timestamp_pattern = r'\d{8}_\d{6}'
        match = re.search(timestamp_pattern, name)
        if match:
            timestamp_pos = name.find(match.group(0))
            if timestamp_pos > 0 and name[timestamp_pos-1] == '_':
                name = name[:timestamp_pos-1]
            else:
                name = name[:timestamp_pos]
                
        return name
    
    def _calculate_filename_similarity(self, file1, file2):
        """计算两个文件名之间的相似度"""
        # 移除扩展名
        name1, _ = os.path.splitext(file1)
        name2, _ = os.path.splitext(file2)
        
        # 移除可能的前缀
        if name1.startswith("preview_"):
            name1 = name1.split("_", 1)[1]
        if name1.startswith("modified_"):
            name1 = name1.split("_", 2)[2] if len(name1.split("_", 2)) > 2 else name1
            
        if name2.startswith("preview_"):
            name2 = name2.split("_", 1)[1]
        if name2.startswith("modified_"):
            name2 = name2.split("_", 2)[2] if len(name2.split("_", 2)) > 2 else name2
        
        # 移除时间戳
        name1 = re.sub(r'\d{8}_\d{6}', '', name1)
        name2 = re.sub(r'\d{8}_\d{6}', '', name2)
        
        # 如果两者相等，直接返回1.0
        if name1 == name2:
            return 1.0
            
        # 简单计算Jaccard相似度
        set1 = set(name1.lower())
        set2 = set(name2.lower())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        if union == 0:
            return 0.0
            
        return intersection / union
    
    def analyze_text_changes(self):
        """分析原始文件和修改后文件的文本变化"""
        try:
            # 如果已有text_changes数据，就跳过分析
            if self.text_changes:
                return
                
            # 创建PPT处理器
            ppt_processor = PPTProcessor(self.api_key_manager, self.text_processor)
            
            # 加载原始和修改后的PPT文件
            original_prs = Presentation(self.original_ppt_path)
            modified_prs = Presentation(self.modified_ppt_path)
            
            # 分析每个幻灯片的文本变化
            self.text_changes = {}
            
            # 确保slides_to_modify不超过实际幻灯片数
            max_slides = min(len(original_prs.slides), len(modified_prs.slides))
            
            for slide_index in self.slides_to_modify:
                if slide_index > max_slides:
                    continue
                    
                # 幻灯片索引从1开始，而Python列表索引从0开始
                original_slide = original_prs.slides[slide_index - 1]
                modified_slide = modified_prs.slides[slide_index - 1]
                
                # 提取和对比文本
                original_items = ppt_processor.extract_slide_texts(original_slide)
                modified_items = ppt_processor.extract_slide_texts(modified_slide)
                
                # 提取文本部分以便比较
                original_texts = [item["text"] for item in original_items]
                modified_texts = [item["text"] for item in modified_items]
                
                changes = []
                
                # 对比文本变化
                for i, (orig_item, mod_item) in enumerate(zip(original_items, modified_items)):
                    original_text = orig_item["text"]
                    modified_text = mod_item["text"]
                    
                    if original_text != modified_text:
                        # 使用原始文本的位置信息
                        position = orig_item.get("position", None)
                        
                        changes.append({
                            "id": str(i),
                            "original": original_text,
                            "modified": modified_text,
                            "position": position  # 保存文本在幻灯片中的位置信息
                        })
                
                # 保存变化信息
                if changes:
                    self.text_changes[slide_index] = {
                        "changes": changes
                    }
                    
                    # 初始化决策记录
                    self.change_decisions[slide_index] = {}
                    for i, change in enumerate(changes):
                        self.change_decisions[slide_index][str(i)] = True  # 默认接受所有修改
            
            # 更新有修改的幻灯片索引集合
            self.slides_with_changes = set(self.text_changes.keys())
            logging.info(f"分析了 {len(self.text_changes)} 张幻灯片的文本变化")
            logging.info(f"有修改的幻灯片索引: {self.slides_with_changes}")
            
        except Exception as e:
            logging.exception(f"分析文本变化时发生错误: {str(e)}")
            QMessageBox.warning(self, "分析错误", f"分析文本变化时发生错误: {str(e)}")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle("预览修改")
        self.setGeometry(100, 100, 1200, 800)
        
        # 应用暗黑风格
        self.apply_dark_theme()
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setStyleSheet("background-color: #2C2C2C;")  # 暗色背景
        
        # 设置主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        
        # 移除顶部信息和工具区域（header_widget, progress_bar, progress_label）
        # 原代码：
        # self.header_widget = QWidget()
        # header_layout = QHBoxLayout(self.header_widget)
        # self.progress_bar = QProgressBar()
        # self.progress_bar.setFixedWidth(150)
        # self.progress_bar.setTextVisible(True)
        # header_layout.addWidget(self.progress_bar)
        # self.progress_label = QLabel("0/0")
        # self.progress_label.setFixedWidth(50)
        # self.progress_label.setAlignment(Qt.AlignCenter)
        # header_layout.addWidget(self.progress_label)
        # main_layout.addWidget(self.header_widget)
        
        # 创建功能性但不可见的header_widget，以便其他代码可以引用
        self.header_widget = QWidget()
        self.header_widget.setVisible(False)
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("0/0")
        
        # 创建分割器作为内容区域
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)
        
        # 创建幻灯片列表区域
        slide_list_widget = QWidget()
        slide_list_widget.setStyleSheet("background-color: #222222; border: none;")
        slide_list_layout = QVBoxLayout(slide_list_widget)
        slide_list_layout.setContentsMargins(0, 0, 0, 0)
        slide_list_layout.setSpacing(0)
        
        slide_list_label = QLabel("幻灯片列表")
        slide_list_label.setAlignment(Qt.AlignCenter)
        slide_list_label.setStyleSheet("font-weight: bold; font-size: 11pt; color: #E0E0E0; padding: 8px; background-color: #222222; border-bottom: 1px solid #333333;")
        slide_list_layout.addWidget(slide_list_label)
        
        # 使用自定义的SlideListWidget替换QListWidget
        self.slide_list = SlideListWidget(self)
        self.slide_list.currentRowChanged.connect(self.change_current_slide)
        self.slide_list.setAlternatingRowColors(True)  # 使用交替行颜色
        self.slide_list.setStyleSheet("""
            QListWidget {
                background-color: #222222;
                alternate-background-color: #262626;
                color: #CCCCCC;
                border: none;
                selection-background-color: #1f1f1f;
                outline: none;
                font-size: 10pt;
            }
            QListWidget::item {
                padding: 6px 0px 6px 10px;
                border: none;
            }
            QListWidget::item:selected {
                background-color: #1f1f1f;
                color: #7DAEA3;
                border-left: 2px solid #7DAEA3;
            }
            QListWidget::item:hover:!selected {
                background-color: #2A2A2A;
            }
        """)
        slide_list_layout.addWidget(self.slide_list)
        
        # 添加全选/全不选按钮
        select_buttons_widget = QWidget()
        select_buttons_widget.setStyleSheet("background-color: #222222;")
        select_buttons_layout = QHBoxLayout(select_buttons_widget)
        select_buttons_layout.setContentsMargins(5, 5, 5, 5)
        
        select_all_button = QPushButton("全部接受")
        select_all_button.clicked.connect(self.select_all_slides)
        select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #2C4136;
                color: #E0E0E0;
                border: 1px solid #2E7D32;
                border-radius: 3px;
                padding: 4px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #2E7D32;
            }
            QPushButton:pressed {
                background-color: #388E3C;
            }
        """)
        
        select_none_button = QPushButton("全部拒绝")
        select_none_button.clicked.connect(self.select_none_slides)
        select_none_button.setStyleSheet("""
            QPushButton {
                background-color: #4D2828;
                color: #E0E0E0;
                border: 1px solid #B71C1C;
                border-radius: 3px;
                padding: 4px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #B71C1C;
            }
            QPushButton:pressed {
                background-color: #D32F2F;
            }
        """)
        
        select_buttons_layout.addWidget(select_all_button)
        select_buttons_layout.addWidget(select_none_button)
        slide_list_layout.addWidget(select_buttons_widget)
        
        # 添加当前幻灯片的接受/拒绝按钮
        decision_buttons_widget = QWidget()
        decision_buttons_widget.setStyleSheet("background-color: #222222;")
        decision_buttons_layout = QHBoxLayout(decision_buttons_widget)
        decision_buttons_layout.setContentsMargins(5, 0, 5, 5)
        
        self.accept_button = QPushButton("接受当前幻灯片")
        self.accept_button.setIcon(QIcon.fromTheme("dialog-ok"))
        self.accept_button.setCursor(Qt.PointingHandCursor)
        self.accept_button.setStyleSheet("""
            QPushButton {
                background-color: #2C4136;
                color: #E0E0E0;
                border: 1px solid #2E7D32;
                border-radius: 3px;
                padding: 4px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #2E7D32;
            }
            QPushButton:pressed {
                background-color: #388E3C;
            }
            QPushButton:disabled {
                background-color: #1A1A1A;
                color: #555555;
                border: 1px solid #2A2A2A;
            }
        """)
        self.accept_button.clicked.connect(lambda: self.set_slide_decision(True))
        
        self.reject_button = QPushButton("拒绝当前幻灯片")
        self.reject_button.setIcon(QIcon.fromTheme("dialog-cancel"))
        self.reject_button.setCursor(Qt.PointingHandCursor)
        self.reject_button.setStyleSheet("""
            QPushButton {
                background-color: #4D2828;
                color: #E0E0E0;
                border: 1px solid #B71C1C;
                border-radius: 3px;
                padding: 4px;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #B71C1C;
            }
            QPushButton:pressed {
                background-color: #D32F2F;
            }
            QPushButton:disabled {
                background-color: #1A1A1A;
                color: #555555;
                border: 1px solid #2A2A2A;
            }
        """)
        self.reject_button.clicked.connect(lambda: self.set_slide_decision(False))
        
        decision_buttons_layout.addWidget(self.accept_button)
        decision_buttons_layout.addWidget(self.reject_button)
        slide_list_layout.addWidget(decision_buttons_widget)
        
        # 添加幻灯片列表到分割器
        splitter.addWidget(slide_list_widget)
        
        # 创建右侧内容区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 创建预览区域分割器
        preview_splitter = QSplitter(Qt.Vertical)
        preview_splitter.setChildrenCollapsible(False)
        
        # 创建幻灯片预览区域
        slides_widget = QWidget()
        slides_layout = QHBoxLayout(slides_widget)
        
        # 创建原始幻灯片预览区域
        original_group = QGroupBox("原始幻灯片")
        original_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444444;
                border-radius: 4px;
                margin-top: 12px;
                padding-top: 15px;
                color: #E0E0E0;
                font-weight: bold;
                background-color: #252525;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                top: 5px;
                padding: 0px 5px;
                background-color: #252525;
            }
        """)
        original_layout = QVBoxLayout(original_group)
        self.original_viewer = SlideViewer()
        self.original_viewer.setStyleSheet("""
            background-color: #1A1A1A;
            border: none;
        """)
        original_layout.addWidget(self.original_viewer)
        slides_layout.addWidget(original_group)
        
        # 创建修改后幻灯片预览区域
        modified_group = QGroupBox("修改后幻灯片")
        modified_group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #444444;
                border-radius: 4px;
                margin-top: 12px;
                padding-top: 15px;
                color: #E0E0E0;
                font-weight: bold;
                background-color: #252525;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                top: 5px;
                padding: 0px 5px;
                background-color: #252525;
            }
        """)
        modified_layout = QVBoxLayout(modified_group)
        self.modified_viewer = SlideViewer()
        self.modified_viewer.setStyleSheet("""
            background-color: #1A1A1A;
            border: none;
        """)
        modified_layout.addWidget(self.modified_viewer)
        slides_layout.addWidget(modified_group)
        
        # 添加幻灯片预览区域到预览分割器
        preview_splitter.addWidget(slides_widget)
        
        # 创建文本修改对比区域
        text_diff_widget = QWidget()
        text_diff_layout = QVBoxLayout(text_diff_widget)
        
        # 创建标题区域（使用水平布局，使标题居中）
        title_container = QWidget()
        title_container.setStyleSheet("background-color: #252525;")
        title_layout = QHBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 5)
        
        # 添加导航按钮到标题区域
        self.prev_text_btn = QPushButton("◀")
        self.prev_text_btn.setFixedSize(30, 25)
        self.prev_text_btn.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                color: #E0E0E0;
                border: 1px solid #555555;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #555555;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #2C5D8F;
            }
            QPushButton:disabled {
                background-color: #252525;
                color: #555555;
                border: 1px solid #333333;
            }
        """)
        self.prev_text_btn.clicked.connect(self.scroll_text_left)
        
        self.next_text_btn = QPushButton("▶")
        self.next_text_btn.setFixedSize(30, 25)
        self.next_text_btn.setStyleSheet("""
            QPushButton {
                background-color: #3A3A3A;
                color: #E0E0E0;
                border: 1px solid #555555;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #555555;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #2C5D8F;
            }
            QPushButton:disabled {
                background-color: #252525;
                color: #555555;
                border: 1px solid #333333;
            }
        """)
        self.next_text_btn.clicked.connect(self.scroll_text_right)
        
        title_layout.addWidget(self.prev_text_btn)
        title_layout.addStretch()
        
        self.text_diff_title = QLabel("文本修改详情（每项修改可单独选择）")
        self.text_diff_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        self.text_diff_title.setStyleSheet("color: #7DAEA3;")
        title_layout.addWidget(self.text_diff_title, 0, Qt.AlignCenter)
        
        title_layout.addStretch()
        title_layout.addWidget(self.next_text_btn)
        
        text_diff_layout.addWidget(title_container)
        
        # 使用滚动区域包含修改列表
        self.text_scroll_area = QScrollArea()  # 保存为实例变量以便后续使用
        self.text_scroll_area.setWidgetResizable(True)
        self.text_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 允许水平滚动
        self.text_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 允许垂直滚动
        self.text_scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #444444;
                border-radius: 4px;
                background-color: #252525;
            }
        """)
        
        # 修改列表容器 - 改为水平布局
        self.changes_container = QWidget()
        self.changes_container.setStyleSheet("background-color: #252525;")
        self.changes_layout = QHBoxLayout(self.changes_container)  # 修改为水平布局
        self.changes_layout.setContentsMargins(10, 10, 10, 10)
        self.changes_layout.setSpacing(15)  # 增加间距使对比框分隔更明显
        
        self.text_scroll_area.setWidget(self.changes_container)
        text_diff_layout.addWidget(self.text_scroll_area)
        
        # 添加文本修改对比区域到预览分割器
        preview_splitter.addWidget(text_diff_widget)
        
        # 设置分割比例
        preview_splitter.setSizes([600, 400])
        
        # 添加预览分割器到右侧布局
        right_layout.addWidget(preview_splitter)
        
        # 添加右侧内容区域到主分割器
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([200, 1000])
        
        # 添加分割器到主布局
        main_layout.addWidget(splitter)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.setContentsMargins(10, 8, 10, 5)
        bottom_layout.addStretch()
        
        # 应用按钮
        apply_button = QPushButton('应用选择')
        apply_button.setFixedWidth(140)  # 增加宽度 110 -> 140
        apply_button.setFixedHeight(40)  # 增加高度 30 -> 40
        apply_button.clicked.connect(self.apply_changes)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #2C4136;
                color: #FFFFFF;  /* 更亮的文字颜色 */
                border: 1px solid #2E7D32;
                border-radius: 3px;  /* 略微增加圆角 */
                padding: 4px;  /* 增加内边距 */
                font-size: 9pt;  /* 增大字体 */
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2E7D32;
                border: 1px solid #3d8b40;  /* 更亮的边框 */
            }
            QPushButton:pressed {
                background-color: #388E3C;
            }
        """)
        
        # 取消按钮
        cancel_button = QPushButton('取消')
        cancel_button.setFixedWidth(110)  # 增加宽度 90 -> 110
        cancel_button.setFixedHeight(40)  # 增加高度 30 -> 40
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #333333;
                color: #FFFFFF;  /* 更亮的文字颜色 */
                border: 1px solid #444444;
                border-radius: 3px;  /* 略微增加圆角 */
                padding: 4px;  /* 增加内边距 */
                font-size: 9pt;  /* 增大字体 */
                font-weight: bold;  /* 加粗文字 */
            }
            QPushButton:hover {
                background-color: #444444;
                border: 1px solid #555555;  /* 更亮的边框 */
            }
            QPushButton:pressed {
                background-color: #555555;
            }
        """)
        
        # 增加按钮之间的间距
        bottom_layout.addStretch()
        bottom_layout.addWidget(apply_button)
        bottom_layout.addSpacing(10)  # 添加固定间距
        bottom_layout.addWidget(cancel_button)
        
        main_layout.addLayout(bottom_layout)
        
        # 初始化幻灯片列表为空
        self.slide_list.clear()
        
        # 创建状态栏
        status_bar = self.statusBar()
        status_bar.showMessage("就绪")
        
        # 显示窗口
        self.show()
    
    def apply_dark_theme(self):
        """应用暗黑风格主题"""
        # 设置全局样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1E1E1E;
                color: #E0E0E0;
            }
            QWidget {
                background-color: #2C2C2C;
                color: #E0E0E0;
            }
            QLabel {
                color: #E0E0E0;
            }
            QSplitter::handle {
                background-color: #444444;
            }
            QStatusBar {
                background-color: #1E1E1E;
                color: #B0B0B0;
                border-top: 1px solid #444444;
            }
            QListWidget {
                background-color: #222222;
                alternate-background-color: #262626;
                color: #CCCCCC;
                border: none;
                border-radius: 0px;
                padding: 0px;
                outline: none;
            }
            QListWidget::item {
                padding: 6px 0px;
                border: none;
                border-radius: 0px;
            }
            QListWidget::item:selected {
                background-color: #1f1f1f;
                color: #7DAEA3;
                border-left: 2px solid #7DAEA3;
            }
            QListWidget::item:hover:!selected {
                background-color: #2A2A2A;
            }
            QPushButton {
                background-color: #444444;
                color: #E0E0E0;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #555555;
                border: 1px solid #666666;
            }
            QPushButton:pressed {
                background-color: #2C5D8F;
            }
            QPushButton:disabled {
                background-color: #2A2A2A;
                color: #666666;
                border: 1px solid #3A3A3A;
            }
            QGroupBox {
                border: 1px solid #444444;
                border-radius: 4px;
                margin-top: 8px;
                font-weight: bold;
                color: #E0E0E0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 3px;
            }
            QScrollBar:vertical {
                background-color: #222222;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #444444;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                background-color: #222222;
                height: 10px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #444444;
                min-width: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
        """)

        # 设置状态栏样式
        self.statusBar().setStyleSheet("""
            QStatusBar {
                background-color: #1E1E1E;
                color: #B0B0B0;
            }
            QStatusBar QLabel {
                color: #B0B0B0;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 5px;
                text-align: center;
                background-color: #2C2C2C;
                color: #E0E0E0;
            }
            QProgressBar::chunk {
                background-color: #3A7BBA;
                width: 10px;
                margin: 0.5px;
            }
        """)
    
    def start_slide_rendering(self):
        """开始渲染幻灯片"""
        try:
            # 不再需要设置progress_bar，因为它是不可见的
            # 原代码：
            # if hasattr(self, 'progress_bar'):
            #     self.progress_bar.setValue(0)
            #     self.progress_bar.setStyleSheet("")
            
            # 确保all_slides已初始化
            if not hasattr(self, 'all_slides') or not self.all_slides:
                logging.warning("all_slides未初始化，使用slides_to_modify替代")
                # 如果slides_to_modify也未初始化，创建默认值
                if not hasattr(self, 'slides_to_modify') or not self.slides_to_modify:
                    # 尝试从PPT文件获取幻灯片数
                    try:
                        prs = Presentation(self.modified_ppt_path)
                        self.slides_to_modify = list(range(1, len(prs.slides) + 1))
                    except Exception as e:
                        logging.error(f"无法从PPT获取幻灯片数: {str(e)}")
                        self.slides_to_modify = [1]  # 设置默认值
                
                self.all_slides = self.slides_to_modify.copy()
                
            # 使用所有幻灯片索引而非只使用slides_to_modify
            self.total_slides = len(self.all_slides) * 2
            self.rendered_slides = 0
            
            # 更新状态栏
            self.statusBar().showMessage("正在渲染幻灯片，请稍候...")
            
            # 确保文件路径是绝对路径
            original_path = os.path.abspath(self.original_ppt_path)
            modified_path = os.path.abspath(self.modified_ppt_path)
            
            # 设置渲染完成标志为False
            self.original_rendering_finished = False
            self.modified_rendering_finished = False
            
            # 创建并启动原始PPT渲染线程 - 渲染所有幻灯片
            self.original_renderer = SlideRenderer(
                original_path, 
                self.all_slides,  # 使用所有幻灯片索引
                self.original_img_dir
            )
            self.original_renderer.slide_ready.connect(self.handle_original_slide_ready)
            self.original_renderer.finished.connect(self.handle_original_rendering_finished)
            self.original_renderer.error.connect(self.handle_rendering_error)
            
            # 短暂延迟后启动修改PPT渲染线程，避免同时启动两个PowerPoint实例
            QApplication.processEvents()
            time.sleep(2)  # 等待2秒确保第一个PowerPoint实例已启动
            
            # 创建并启动修改后PPT渲染线程 - 渲染所有幻灯片
            self.modified_renderer = SlideRenderer(
                modified_path, 
                self.all_slides,  # 使用所有幻灯片索引
                self.modified_img_dir
            )
            self.modified_renderer.slide_ready.connect(self.handle_modified_slide_ready)
            self.modified_renderer.finished.connect(self.handle_modified_rendering_finished)
            self.modified_renderer.error.connect(self.handle_rendering_error)
            
            # 启动渲染线程
            self.original_renderer.start()
            self.modified_renderer.start()
            
        except Exception as e:
            logging.error(f"启动渲染线程失败: {str(e)}")
            QMessageBox.warning(self, "渲染错误", f"启动渲染线程失败: {str(e)}")
            self.statusBar().showMessage("渲染失败")
    
    def handle_original_slide_ready(self, slide_index, image_path):
        """处理原始幻灯片图像就绪事件"""
        # 将图像添加到列表并更新索引映射
        while len(self.original_slides) <= slide_index:
            self.original_slides.append(None)
        
        self.original_slides[slide_index - 1] = image_path
        self.rendered_slides += 1
        self.update_progress()
        
        # 如果是当前选中的幻灯片，更新预览
        if self.slide_list.currentRow() >= 0 and self.slides_to_modify[self.slide_list.currentRow()] == slide_index:
            self.original_viewer.display_slide(image_path, True)  # 明确指定为原始幻灯片
    
    def handle_modified_slide_ready(self, slide_index, image_path):
        """处理修改后幻灯片图像就绪事件"""
        # 将图像添加到列表并更新索引映射
        while len(self.modified_slides) <= slide_index:
            self.modified_slides.append(None)
        
        self.modified_slides[slide_index - 1] = image_path
        self.rendered_slides += 1
        self.update_progress()
        
        # 如果是当前选中的幻灯片，更新预览
        if self.slide_list.currentRow() >= 0 and self.slides_to_modify[self.slide_list.currentRow()] == slide_index:
            self.modified_viewer.display_slide(image_path, False)  # 明确指定不是原始幻灯片
    
    def handle_original_rendering_finished(self):
        """原始幻灯片渲染完成处理"""
        self.original_rendering_finished = True
        logging.info("原始幻灯片渲染完成")
        # 检查所有渲染是否完成
        self.check_all_rendering_finished()
    
    def handle_modified_rendering_finished(self):
        """修改后幻灯片渲染完成处理"""
        self.modified_rendering_finished = True
        logging.info("修改后幻灯片渲染完成")
        # 检查所有渲染是否完成
        self.check_all_rendering_finished()
    
    def handle_rendering_error(self, error_message):
        """处理渲染错误"""
        logging.error(f"渲染错误: {error_message}")
        
        # 如果错误与COM初始化相关，显示更友好的错误信息
        if "CoInitialize" in error_message:
            QMessageBox.warning(
                self, 
                "渲染错误", 
                "PowerPoint渲染初始化失败。请确保您已安装Microsoft PowerPoint，并且没有其他程序正在使用它。\n\n"
                "您可以尝试关闭所有PowerPoint窗口后重试。"
            )
        else:
            QMessageBox.warning(self, "渲染错误", error_message)
            
        # 不再需要设置不可见的progress_bar
        # 原代码：
        # self.progress_bar.setValue(0)
        # self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: #F44336; }")
        
        # 更新状态栏
        self.statusBar().showMessage("渲染失败，请检查PowerPoint安装")
    
    def update_progress(self):
        """更新进度显示"""
        try:
            progress = int(self.rendered_slides / self.total_slides * 100)
            
            # 不再需要更新不可见的progress_bar
            # 原代码：
            # if hasattr(self, 'progress_bar'):
            #     self.progress_bar.setValue(progress)
                
            # 直接使用状态栏显示进度
            self.statusBar().showMessage(f"渲染进度: {progress}%")
        except Exception as e:
            logging.error(f"更新进度失败: {str(e)}")
    
    def check_all_rendering_finished(self):
        """检查所有渲染是否完成"""
        try:
            if hasattr(self, 'original_rendering_finished') and hasattr(self, 'modified_rendering_finished'):
                # 如果两个线程都完成了
                if self.original_rendering_finished and self.modified_rendering_finished:
                    # 处理UI事件
                    QApplication.processEvents()
                    time.sleep(0.5)
                    
                    # 移除隐藏进度指示的代码，因为header_widget已在初始化时设为不可见
                    # 原代码：if hasattr(self, 'header_widget'): self.header_widget.setVisible(False)
                    
                    # 初始化幻灯片显示
                    self.initialize_slides()
                    
                    # 将窗口最大化
                    self.showMaximized()
                    
                    logging.info("所有幻灯片渲染完成，窗口已最大化")
                    self.statusBar().showMessage("渲染完成，可以开始对比")
        except Exception as e:
            logging.error(f"检查渲染完成状态失败: {str(e)}")
            self.statusBar().showMessage("渲染状态检查失败")
    
    def initialize_slides_ui(self):
        """
        初始化幻灯片UI元素（在渲染开始前）
        """
        # 确保all_slides已初始化
        if not hasattr(self, 'all_slides') or not self.all_slides:
            logging.warning("initialize_slides_ui: all_slides未初始化，尝试初始化")
            if hasattr(self, 'slides_to_modify') and self.slides_to_modify:
                self.all_slides = self.slides_to_modify.copy()
            else:
                logging.error("无法初始化all_slides，slides_to_modify也为空")
                return
        
        # 确保slides_with_changes已初始化
        if not hasattr(self, 'slides_with_changes'):
            self.slides_with_changes = set(self.text_changes.keys() if self.text_changes else [])
        
        # 更新幻灯片列表UI
        if hasattr(self, 'slide_list'):
            self.slide_list.clear()  # 清空列表
            
            # 日志记录所有幻灯片索引和有修改的幻灯片索引
            logging.info(f"所有幻灯片索引: {self.all_slides}")
            logging.info(f"有修改的幻灯片索引: {self.slides_with_changes}")
            
            # 添加所有幻灯片到列表
            for i, slide_idx in enumerate(self.all_slides):
                item = QListWidgetItem(f"幻灯片 {slide_idx}")
                
                # 设置不同的颜色和标记，区分有无修改的幻灯片
                if slide_idx in self.slides_with_changes:
                    # 有修改的幻灯片，使用蓝色或其他明显颜色
                    item.setForeground(QColor(0, 120, 215))  # 蓝色
                    item.setText(f"幻灯片 {slide_idx} *")  # 添加星号标记
                    logging.debug(f"添加有修改的幻灯片: 列表索引={i}, 幻灯片索引={slide_idx}")
                else:
                    logging.debug(f"添加无修改的幻灯片: 列表索引={i}, 幻灯片索引={slide_idx}")
                
                self.slide_list.addItem(item)
            
            # 记录幻灯片列表项数量
            logging.info(f"幻灯片列表项数量: {self.slide_list.count()}")
            
            # 选择第一张幻灯片
            if self.slide_list.count() > 0:
                self.slide_list.setCurrentRow(0)
                first_slide_idx = self.all_slides[0] if self.all_slides else 0
                logging.info(f"选择第一张幻灯片: 列表索引=0, 幻灯片索引={first_slide_idx}")
        
        # 更新进度指示器
        self.update_progress_indicator()
        
        # 更新导航按钮状态
        self.update_navigation_buttons()
        
        # 显示加载提示
        if hasattr(self, 'original_viewer') and hasattr(self, 'modified_viewer'):
            self.original_viewer.show_message("正在加载原始幻灯片...")
            self.modified_viewer.show_message("正在加载修改后幻灯片...")
    
    def initialize_slides(self):
        """
        初始化幻灯片显示
        """
        # 检查是否已加载图像
        if not self.original_slides or not self.modified_slides:
            logging.warning("尚未加载幻灯片图像")
            return
        
        # 确保SlideViewer实例已初始化
        if not hasattr(self, 'original_viewer') or not hasattr(self, 'modified_viewer'):
            logging.error("SlideViewer实例未初始化")
            return
            
        # 将加载的图像路径设置到SlideViewer
        self.original_viewer.images = self.original_slides
        self.modified_viewer.images = self.modified_slides
        
        # 设置文本修改数据
        if hasattr(self, 'text_changes') and self.text_changes:
            self.modified_viewer.text_changes = self.text_changes
        
        # 显示第一张幻灯片（如果有）
        if self.slide_list and self.slide_list.count() > 0:
            self.slide_list.setCurrentRow(0)
            self.show_current_slide()
        else:
            # 如果没有幻灯片，显示提示消息
            if hasattr(self, 'original_viewer'):
                self.original_viewer.show_message("无可用幻灯片")
            if hasattr(self, 'modified_viewer'):
                self.modified_viewer.show_message("无可用幻灯片")
        
        # 更新进度指示器
        self.update_progress_indicator()
        
        # 更新导航按钮状态
        self.update_navigation_buttons()
    
    def change_current_slide(self, row):
        """
        切换当前显示的幻灯片
        
        Args:
            row: 幻灯片列表中的行索引
        """
        if row < 0 or row >= len(self.all_slides):
            logging.warning(f"无效的幻灯片行索引: {row}, 有效范围: 0-{len(self.all_slides)-1}")
            return
        
        # 设置当前幻灯片索引
        self.current_slide = row
        
        # 显示当前幻灯片
        self.show_current_slide()
        
        # 使用all_slides获取真实的幻灯片索引，而不是slides_to_modify
        slide_index = self.all_slides[row]
        logging.info(f"切换到幻灯片行 {row}，实际幻灯片索引: {slide_index}")
        
        # 更新文本对比
        self.update_text_comparison(slide_index)
    
    def update_text_comparison(self, slide_index):
        """更新文本比较区域，显示当前幻灯片的所有文本修改
        
        Args:
            slide_index: 当前幻灯片索引
        """
        # 清除现有的所有修改项
        self.clear_layout(self.changes_layout)
        
        # 检查该幻灯片是否有文本修改
        if slide_index not in self.text_changes:
            # 如果没有修改，显示一个提示标签
            no_changes_label = QLabel("此幻灯片没有文本修改")
            no_changes_label.setAlignment(Qt.AlignCenter)
            no_changes_label.setStyleSheet("font-size: 12pt; color: #757575; padding: 20px;")
            self.changes_layout.addWidget(no_changes_label)
            # 更新标题
            self.text_diff_title.setText(f"幻灯片 {slide_index} - 文本修改详情（无修改）")
            # 禁用导航按钮
            self.prev_text_btn.setEnabled(False)
            self.next_text_btn.setEnabled(False)
            return
            
        # 获取该幻灯片的所有修改
        changes = []
        if isinstance(self.text_changes[slide_index], dict) and "changes" in self.text_changes[slide_index]:
            changes = self.text_changes[slide_index]["changes"]
        elif isinstance(self.text_changes[slide_index], list):
            changes = self.text_changes[slide_index]
        else:
            logging.error(f"不支持的文本修改格式: {type(self.text_changes[slide_index])}")
            # 显示一个错误标签
            error_label = QLabel(f"无法识别的修改格式: {type(self.text_changes[slide_index])}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("font-size: 12pt; color: #F44336; padding: 20px;")
            self.changes_layout.addWidget(error_label)
            # 更新标题
            self.text_diff_title.setText(f"幻灯片 {slide_index} - 文本修改详情（格式错误）")
            # 禁用导航按钮
            self.prev_text_btn.setEnabled(False)
            self.next_text_btn.setEnabled(False)
            return
        
        # 确保该幻灯片的决策字典存在
        if slide_index not in self.change_decisions:
            self.change_decisions[slide_index] = {}
        
        # 更新标题
        self.text_diff_title.setText(f"幻灯片 {slide_index} - 文本修改详情（{len(changes)} 处）")
        
        # 启用导航按钮
        self.prev_text_btn.setEnabled(len(changes) > 0)
        self.next_text_btn.setEnabled(len(changes) > 0)
        
        # 添加每个修改项 - 水平排列
        for i, change in enumerate(changes):
            change_id = f"{slide_index}_{i}"
            
            # 创建修改项的容器
            change_widget = QWidget()
            change_layout = QVBoxLayout(change_widget)
            change_layout.setContentsMargins(10, 10, 10, 10)
            change_layout.setSpacing(8)
            
            # 设置change_id属性，用于后续查找
            change_widget.setProperty("change_id", change_id)
            change_widget.setFixedWidth(280)  # 固定宽度，便于水平排列
            
            # 添加序号标签
            index_label = QLabel(f"修改 #{i+1}")
            index_label.setStyleSheet("font-weight: bold; color: #7DAEA3; font-size: 10pt;")
            index_label.setAlignment(Qt.AlignCenter)
            change_layout.addWidget(index_label)
            
            # 获取原文本和修改后文本，兼容不同字段名
            original_text = ""
            modified_text = ""
            
            if isinstance(change, dict):
                # 尝试各种可能的字段名
                if "original_text" in change:
                    original_text = change["original_text"]
                elif "original" in change:
                    original_text = change["original"]
                
                if "modified_text" in change:
                    modified_text = change["modified_text"]
                elif "modified" in change:
                    modified_text = change["modified"]
            else:
                logging.warning(f"修改项不是字典: {type(change)}")
                continue
            
            # 原文本区域
            original_group = QGroupBox("原文本")
            original_group.setStyleSheet("""
                QGroupBox {
                    font-size: 9pt;
                    font-weight: bold;
                    border: 1px solid #444444;
                    border-radius: 4px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background-color: #2A2A2A;
                    color: #E0E0E0;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 3px;
                    color: #B0B0B0;
                }
            """)
            original_layout = QVBoxLayout(original_group)
            original_layout.setContentsMargins(5, 15, 5, 5)
            
            # 使用文本浏览器替代标签，以便支持滚动和更好的文本显示
            original_text_browser = QTextBrowser()
            original_text_browser.setMaximumHeight(80)
            original_text_browser.setText(original_text)
            original_text_browser.setStyleSheet("""
                QTextBrowser {
                    background-color: #333333;
                    color: #E0E0E0;
                    border: 1px solid #444444;
                    border-radius: 3px;
                    padding: 3px;
                    font-size: 9pt;
                }
            """)
            original_layout.addWidget(original_text_browser)
            change_layout.addWidget(original_group)
            
            # 修改后文本区域
            modified_group = QGroupBox("修改后文本")
            modified_group.setStyleSheet("""
                QGroupBox {
                    font-size: 9pt;
                    font-weight: bold;
                    border: 1px solid #444444;
                    border-radius: 4px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background-color: #2A2A2A;
                    color: #E0E0E0;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 3px;
                    color: #7DAEA3;
                }
            """)
            modified_layout = QVBoxLayout(modified_group)
            modified_layout.setContentsMargins(5, 15, 5, 5)
            
            # 使用文本编辑器替代文本浏览器，以便支持编辑功能
            modified_text_edit = QTextEdit()
            modified_text_edit.setMaximumHeight(80)
            modified_text_edit.setText(modified_text)
            modified_text_edit.setStyleSheet("""
                QTextEdit {
                    background-color: #333333;
                    color: #7DAEA3;
                    border: 1px solid #444444;
                    border-radius: 3px;
                    padding: 3px;
                    font-size: 9pt;
                }
            """)
            modified_layout.addWidget(modified_text_edit)
            change_layout.addWidget(modified_group)
            
            # 添加决策按钮（接受/拒绝/手动修改单个修改）
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 5, 0, 0)
            buttons_layout.setSpacing(10)
            
            # 创建接受、拒绝和手动修改按钮
            accept_change_btn = QPushButton("接受")
            accept_change_btn.setIcon(QIcon.fromTheme("dialog-ok"))
            accept_change_btn.setCursor(Qt.PointingHandCursor)
            accept_change_btn.setStyleSheet("""
                QPushButton {
                    background-color: #333333;
                    color: #E0E0E0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 9pt;
                }
                QPushButton:hover {
                    background-color: #3F3F3F;
                    border: 1px solid #7DAEA3;
                }
                QPushButton:pressed {
                    background-color: #2A2A2A;
                }
                QPushButton:disabled {
                    background-color: #2A2A2A;
                    color: #666666;
                    border: 1px solid #444444;
                }
            """)
            
            reject_change_btn = QPushButton("拒绝")
            reject_change_btn.setIcon(QIcon.fromTheme("dialog-cancel"))
            reject_change_btn.setCursor(Qt.PointingHandCursor)
            reject_change_btn.setStyleSheet("""
                QPushButton {
                    background-color: #333333;
                    color: #E0E0E0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 9pt;
                }
                QPushButton:hover {
                    background-color: #3F3F3F;
                    border: 1px solid #F44336;
                }
                QPushButton:pressed {
                    background-color: #2A2A2A;
                }
                QPushButton:disabled {
                    background-color: #2A2A2A;
                    color: #666666;
                    border: 1px solid #444444;
                }
            """)
            
            manual_edit_btn = QPushButton("保存修改")
            manual_edit_btn.setIcon(QIcon.fromTheme("document-save"))
            manual_edit_btn.setCursor(Qt.PointingHandCursor)
            manual_edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #333333;
                    color: #E0E0E0;
                    border: 1px solid #555555;
                    border-radius: 3px;
                    padding: 5px 10px;
                    font-size: 9pt;
                }
                QPushButton:hover {
                    background-color: #3F3F3F;
                    border: 1px solid #2196F3;
                }
                QPushButton:pressed {
                    background-color: #2A2A2A;
                }
            """)
            
            # 连接按钮信号
            accept_change_btn.clicked.connect(lambda checked, cid=change_id, sidx=slide_index: self.set_change_decision(sidx, cid, True))
            reject_change_btn.clicked.connect(lambda checked, cid=change_id, sidx=slide_index: self.set_change_decision(sidx, cid, False))
            manual_edit_btn.clicked.connect(lambda checked, editor=modified_text_edit, cid=change_id, sidx=slide_index: self.save_manual_edit(editor, sidx, cid))
            
            # 添加按钮到布局
            buttons_layout.addWidget(accept_change_btn)
            buttons_layout.addWidget(reject_change_btn)
            buttons_layout.addWidget(manual_edit_btn)
            change_layout.addWidget(buttons_widget)
            
            # 设置修改项的样式，基于当前决策
            is_accepted = self.change_decisions[slide_index].get(change_id, None)
            if is_accepted is True:
                change_widget.setStyleSheet("border: 2px solid #2E7D32; border-radius: 5px; background-color: #263A28;")
                accept_change_btn.setEnabled(False)
                reject_change_btn.setEnabled(True)
            elif is_accepted is False:
                change_widget.setStyleSheet("border: 2px solid #B71C1C; border-radius: 5px; background-color: #3A2828;")
                accept_change_btn.setEnabled(True)
                reject_change_btn.setEnabled(False)
            else:
                change_widget.setStyleSheet("border: 1px solid #444444; border-radius: 5px; background-color: #2A2A2A;")
                accept_change_btn.setEnabled(True)
                reject_change_btn.setEnabled(True)
            
            # 添加整个修改项到主布局
            self.changes_layout.addWidget(change_widget)
            
        # 添加一个空的widget作为末尾填充，确保所有修改项可以滚动到最右侧
        if len(changes) > 0:
            spacer = QWidget()
            spacer.setFixedWidth(10)
            self.changes_layout.addWidget(spacer)
    
    def clear_layout(self, layout):
        """清除布局中的所有部件
        
        Args:
            layout: 要清除的布局
        """
        if layout is None:
            return
            
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()
            else:
                # 如果是子布局，递归清除
                self.clear_layout(item.layout())
    
    def set_change_decision(self, slide_index, change_id, accept):
        """设置单个修改项的决策
        
        Args:
            slide_index: 幻灯片索引
            change_id: 修改项ID
            accept: 是否接受修改
        """
        logging.debug(f"设置修改决策: 幻灯片={slide_index}, 修改项={change_id}, 接受={accept}")
        
        # 确保决策字典存在
        if slide_index not in self.change_decisions:
            self.change_decisions[slide_index] = {}
            
        # 设置决策
        self.change_decisions[slide_index][change_id] = accept
        
        # 更新UI
        self.update_text_comparison(slide_index)
        
        # 检查该幻灯片的所有修改是否都有相同的决策
        all_same = True
        changes = self.text_changes[slide_index]["changes"]
        decision = accept
        
        for i in range(len(changes)):
            c_id = f"{slide_index}_{i}"
            if self.change_decisions[slide_index].get(c_id, None) != decision:
                all_same = False
                break
                
        # 如果所有修改项都有相同的决策，更新幻灯片的整体决策
        if all_same:
            self.user_choices[slide_index] = decision
            
            # 更新幻灯片列表项 - 使用all_slides查找行索引
            try:
                current_index = self.all_slides.index(slide_index) if slide_index in self.all_slides else -1
                logging.debug(f"更新幻灯片列表: 幻灯片索引={slide_index}, 列表索引={current_index}")
                
                if current_index >= 0:
                    item = self.slide_list.item(current_index)
                    if item:
                        if decision:
                            item.setText(f"幻灯片 {slide_index} ✓")
                            item.setForeground(QColor("#4CAF50"))  # 绿色
                        else:
                            item.setText(f"幻灯片 {slide_index} ✗")
                            item.setForeground(QColor("#F44336"))  # 红色
            except Exception as e:
                logging.error(f"更新幻灯片列表项时出错: {str(e)}")
            
            # 更新决策按钮状态
            self.update_decision_buttons(slide_index)
    
    def connect_preview_signals(self):
        """连接预览窗口的所有信号"""
        # 连接原始预览的区域点击信号
        if hasattr(self, 'original_viewer') and self.original_viewer:
            self.original_viewer.textRegionClicked.connect(self.handle_region_click)
            self.original_viewer.textRegionRejected.connect(self.handle_region_rejected)
            
        # 连接修改后预览的区域点击信号
        if hasattr(self, 'modified_viewer') and self.modified_viewer:
            self.modified_viewer.textRegionClicked.connect(self.handle_region_click)
            self.modified_viewer.textRegionRejected.connect(self.handle_region_rejected)
    
    def handle_region_click(self, region_id):
        """
        处理文本区域点击事件
        
        Args:
            region_id: 被点击的区域ID，格式为 "slide_index_change_index"
        """
        logging.debug(f"PreviewWindow处理文本区域点击: {region_id}")
        
        # 分解区域ID获取幻灯片索引和修改索引
        try:
            parts = region_id.split('_')
            if len(parts) >= 2:
                slide_index = int(parts[0])
                change_index = int(parts[1])
                change_id = f"{slide_index}_{change_index}"
                
                # 确保当前显示的是正确的幻灯片
                if slide_index != self.current_slide_index:
                    # 如果不是当前幻灯片，切换到对应的幻灯片
                    logging.debug(f"切换到幻灯片 {slide_index}")
                    
                    # 使用all_slides查找行索引
                    try:
                        row = self.all_slides.index(slide_index) if slide_index in self.all_slides else -1
                        logging.debug(f"查找幻灯片行索引: 幻灯片={slide_index}, 行索引={row}")
                        
                        if row >= 0:
                            self.slide_list.setCurrentRow(row)
                        else:
                            logging.warning(f"在all_slides中找不到幻灯片 {slide_index}")
                    except Exception as e:
                        logging.error(f"查找幻灯片索引时出错: {str(e)}")
                    return
                
                # 在修改列表中高亮对应的修改项
                if hasattr(self, 'change_items') and change_id in self.change_items:
                    # 高亮修改项
                    logging.debug(f"高亮修改项: {change_id}")
                    self.scrollToChangeItem(self.change_items[change_id])
                else:
                    logging.warning(f"找不到对应的修改项: {change_id}")
        except Exception as e:
            logging.error(f"处理区域点击时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            
    def handle_region_rejected(self, region_id, is_rejected):
        """
        处理文本区域拒绝状态变化
        
        Args:
            region_id: 文本区域ID，格式为 "slide_index_change_index"
            is_rejected: 是否拒绝该修改
        """
        logging.debug(f"处理文本区域拒绝状态: {region_id}, 拒绝={is_rejected}")
        
        try:
            parts = region_id.split('_')
            if len(parts) >= 2:
                slide_index = int(parts[0])
                change_index = int(parts[1])
                change_id = f"{slide_index}_{change_index}"
                
                # 设置拒绝状态 - 调用set_change_decision方法来确保完整的拒绝操作流程
                # 注意：拒绝状态(False)对应接受参数的反值
                self.set_change_decision(slide_index, change_id, not is_rejected)
                
                # 同步图像上的拒绝状态到修改列表
                if hasattr(self, 'change_items') and change_id in self.change_items:
                    change_item = self.change_items[change_id]
                    if is_rejected:
                        # 拒绝修改
                        if hasattr(change_item, 'reject_change'):
                            change_item.reject_change()
                    else:
                        # 接受修改
                        if hasattr(change_item, 'accept_change'):
                            change_item.accept_change()
                
                # 确保区域状态在原始和修改后视图中保持一致
                if hasattr(self, 'original_viewer') and hasattr(self, 'modified_viewer'):
                    # 修复同步逻辑：需要找到正确的区域ID
                    for viewer in [self.original_viewer, self.modified_viewer]:
                        if hasattr(viewer, 'text_region_items'):
                            # 区域ID在viewer中可能只是change_index，而不是完整的slide_index_change_index
                            region_keys = [k for k in viewer.text_region_items.keys() if str(change_index) == str(k) or region_id == k]
                            for k in region_keys:
                                region_item = viewer.text_region_items[k]
                                if hasattr(region_item, 'set_rejected'):
                                    region_item.set_rejected(is_rejected)
                                    logging.debug(f"在{viewer.__class__.__name__}中设置区域{k}的拒绝状态为{is_rejected}")
        except Exception as e:
            logging.error(f"处理区域拒绝状态时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
    
    def scroll_to_change_item(self, change_id):
        """滚动到指定的修改项
        
        Args:
            change_id: 修改项ID
        """
        # 遍历changes_layout中的所有部件，找到对应ID的修改项
        for i in range(self.changes_layout.count()):
            widget = self.changes_layout.itemAt(i).widget()
            if widget and isinstance(widget, QWidget) and widget.property("change_id") == change_id:
                # 找到对应的修改项，滚动到它的位置
                scroll_area = self.findChild(QScrollArea, "changes_scroll_area")
                if scroll_area:
                    # 获取部件在滚动区域中的相对位置
                    scroll_area.ensureWidgetVisible(widget)
                    
                    # 高亮显示该修改项
                    original_style = widget.styleSheet()
                    widget.setStyleSheet(original_style + "; background-color: #E3F2FD;")
                    
                    # 短暂高亮后恢复原样式
                    QTimer.singleShot(1500, lambda w=widget, s=original_style: w.setStyleSheet(s))
                return
    
    def highlight_change_item(self, change_id):
        """高亮显示指定ID的修改项"""
        # 获取当前幻灯片索引
        current_row = self.slide_list.currentRow()
        if current_row < 0 or current_row >= len(self.all_slides):
            logging.warning(f"无效的行索引: {current_row}")
            return
            
        # 使用all_slides获取真实的幻灯片索引
        slide_index = self.all_slides[current_row]
        logging.debug(f"高亮修改项: 行索引={current_row}, 幻灯片索引={slide_index}, 修改项ID={change_id}")
        
        change_id_full = f"{slide_index}_{change_id}"
        
        if hasattr(self, 'change_items') and change_id_full in self.change_items:
            # 获取修改项
            item = self.change_items[change_id_full]
            
            # 高亮修改项
            item.setStyleSheet("""
                TextChangeItem {
                    border: 2px solid #2196F3;
                    border-radius: 3px;
                    padding: 5px;
                    background-color: #1E2A38;
                }
            """)
            
            # 确保修改项可见
            self.scrollToChangeItem(item)
        else:
            logging.warning(f"找不到修改项: {change_id_full}")
    
    def scrollToChangeItem(self, item):
        """滚动到指定的修改项"""
        # 获取包含修改项的滚动区域
        changes_scroll_area = None
        
        # 遍历主布局查找滚动区域
        for i in range(self.centralWidget().layout().count()):
            widget = self.centralWidget().layout().itemAt(i).widget()
            if isinstance(widget, QSplitter):
                # 找到右侧部件
                right_widget = widget.widget(1)
                # 查找右侧布局中的滚动区域
                for j in range(right_widget.layout().count()):
                    w = right_widget.layout().itemAt(j).widget()
                    if isinstance(w, QWidget) and w.layout():
                        for k in range(w.layout().count()):
                            scroll = w.layout().itemAt(k).widget()
                            if isinstance(scroll, QScrollArea):
                                changes_scroll_area = scroll
                                break
        
        # 如果找到滚动区域，滚动到修改项位置
        if changes_scroll_area:
            # 将项目滚动到视图中央
            changes_scroll_area.ensureWidgetVisible(item, 50, 100)
            
            # 为了明显地标记当前项目，短暂闪烁一下
            original_style = item.styleSheet()
            item.setStyleSheet("""
                TextChangeItem {
                    border: 2px solid #FF5722;
                    border-radius: 3px;
                    padding: 5px;
                    background-color: #3E2723;
                }
            """)
            
            # 使用定时器在短暂时间后恢复样式
            QTimer.singleShot(500, lambda: item.setStyleSheet(original_style))
    
    def clear_change_item_highlights(self):
        """清除所有修改项的高亮"""
        for item in self.change_items.values():
            item.setStyleSheet("""
                TextChangeItem {
                    border: 1px solid #3C3F41;
                    border-radius: 3px;
                    padding: 5px;
                    background-color: #2C2F30;
                }
            """)
    
    def highlight_text_region(self, change_id):
        """在幻灯片图像上高亮对应的文本区域"""
        # 提取修改项索引
        parts = change_id.split('_')
        if len(parts) == 2:
            change_index = parts[1]
            
            # 高亮原始幻灯片和修改后幻灯片中的相应区域
            self.original_viewer.highlight_region(change_index)
            self.modified_viewer.highlight_region(change_index)
    
    def clear_text_region_highlights(self):
        """清除幻灯片图像上的所有高亮"""
        self.original_viewer.highlight_region(None)
        self.modified_viewer.highlight_region(None)
    
    def update_decision_buttons(self, slide_index):
        """更新决策按钮状态
        
        Args:
            slide_index: 当前幻灯片索引
        """
        logging.debug(f"更新决策按钮状态: 幻灯片={slide_index}")
        
        # 检查所需的按钮是否存在
        has_required_buttons = all(hasattr(self, btn) for btn in ['accept_button', 'reject_button'])
        if not has_required_buttons:
            logging.warning("缺少必要的按钮属性，无法更新按钮状态")
            return
            
        # 如果幻灯片没有修改，禁用决策按钮
        if slide_index not in self.slides_with_changes:
            logging.debug(f"幻灯片 {slide_index} 没有修改，禁用接受/拒绝按钮")
            self.accept_button.setEnabled(False)
            self.reject_button.setEnabled(False)
            
            # 全局批量按钮保持启用
            if hasattr(self, 'accept_all_button'):
                self.accept_all_button.setEnabled(True)  
            if hasattr(self, 'reject_all_button'):
                self.reject_all_button.setEnabled(True)
            return
        
        # 根据当前决策状态更新按钮
        if slide_index in self.user_choices:
            decision = self.user_choices[slide_index]
            if decision is True:  # 已接受
                logging.debug(f"幻灯片 {slide_index} 已接受，禁用接受按钮")
                self.accept_button.setEnabled(False)
                self.reject_button.setEnabled(True)
            elif decision is False:  # 已拒绝
                logging.debug(f"幻灯片 {slide_index} 已拒绝，禁用拒绝按钮")
                self.accept_button.setEnabled(True)
                self.reject_button.setEnabled(False)
            else:  # 混合状态
                logging.debug(f"幻灯片 {slide_index} 为混合状态，启用全部按钮")
                self.accept_button.setEnabled(True)
                self.reject_button.setEnabled(True)
        else:
            # 未做决策
            logging.debug(f"幻灯片 {slide_index} 未做决策，启用全部按钮")
            self.accept_button.setEnabled(True)
            self.reject_button.setEnabled(True)
            
        # 确保全局按钮始终启用
        if hasattr(self, 'accept_all_button'):
            self.accept_all_button.setEnabled(True)
        if hasattr(self, 'reject_all_button'):
            self.reject_all_button.setEnabled(True)
    
    def set_slide_decision(self, accept: bool):
        """
        设置当前幻灯片的决策状态（接受或拒绝所有修改）
        
        Args:
            accept: 是否接受修改
        """
        if not self.slide_list.currentItem():
            logging.warning("未选中任何幻灯片，无法设置决策")
            return
            
        # 获取当前幻灯片索引
        current_row = self.slide_list.currentRow()
        if current_row < 0 or current_row >= len(self.all_slides):
            logging.warning(f"无效的幻灯片行索引: {current_row}, 有效范围: 0-{len(self.all_slides)-1}")
            return
        
        # 使用all_slides获取真实的幻灯片索引    
        slide_index = self.all_slides[current_row]
        logging.info(f"设置幻灯片决策: 行索引={current_row}, 幻灯片索引={slide_index}, 接受={accept}")
        
        # 检查此幻灯片是否有修改
        if slide_index not in self.slides_with_changes:
            logging.warning(f"幻灯片 {slide_index} 没有修改项，无需设置决策")
            return
        
        # 记录整体决策
        if not hasattr(self, 'user_choices'):
            self.user_choices = {}
        self.user_choices[slide_index] = accept
        
        # 更新当前幻灯片的所有修改项的决策
        if slide_index in self.text_changes:
            changes = self.text_changes[slide_index]["changes"]
            logging.debug(f"幻灯片 {slide_index} 有 {len(changes)} 个修改项需要更新决策")
            
            # 更新变更决策字典
            if slide_index not in self.change_decisions:
                self.change_decisions[slide_index] = {}
                
            for i in range(len(changes)):
                change_id = f"{slide_index}_{i}"
                self.change_decisions[slide_index][change_id] = accept
                logging.debug(f"设置修改项决策: {change_id} = {accept}")
                
                # 更新UI中的修改项状态
                if hasattr(self, 'change_items') and change_id in self.change_items:
                    self.change_items[change_id].set_decision(accept)
                    logging.debug(f"更新修改项UI: {change_id}")
        else:
            logging.warning(f"幻灯片 {slide_index} 在text_changes中未找到，无法更新修改项")
        
        # 更新幻灯片列表项的显示
        current_item = self.slide_list.currentItem()
        if current_item:
            if accept:
                current_item.setText(f"幻灯片 {slide_index} ✓")
                current_item.setForeground(QColor("#4CAF50"))  # 绿色
            else:
                current_item.setText(f"幻灯片 {slide_index} ✗")
                current_item.setForeground(QColor("#F44336"))  # 红色
            logging.debug(f"更新幻灯片列表项: {slide_index} 为 {'接受' if accept else '拒绝'}")
        
        # 更新决策按钮状态
        self.update_decision_buttons(slide_index)
    
    def select_all_slides(self):
        """选择所有幻灯片（全部接受修改）"""
        logging.info("设置所有幻灯片为接受状态")
        
        if not hasattr(self, 'user_choices'):
            self.user_choices = {}
            
        # 遍历所有有修改的幻灯片，设置为接受
        for slide_index in self.slides_with_changes:
            # 记录用户选择
            self.user_choices[slide_index] = True
            logging.debug(f"设置幻灯片 {slide_index} 为接受状态")
            
            # 更新幻灯片的所有修改项决策
            if slide_index in self.text_changes:
                changes = self.text_changes[slide_index].get("changes", [])
                
                # 确保决策字典存在
                if slide_index not in self.change_decisions:
                    self.change_decisions[slide_index] = {}
                    
                # 更新所有修改项的决策
                for j in range(len(changes)):
                    change_id = f"{slide_index}_{j}"
                    self.change_decisions[slide_index][change_id] = True
                    logging.debug(f"设置修改项 {change_id} 为接受状态")
            
            # 更新列表项显示 - 通过all_slides查找行索引
            try:
                list_idx = self.all_slides.index(slide_index) if slide_index in self.all_slides else -1
                if list_idx >= 0 and list_idx < self.slide_list.count():
                    item = self.slide_list.item(list_idx)
                    if item:
                        item.setText(f"幻灯片 {slide_index} ✓")
                        item.setForeground(QColor("#4CAF50"))  # 绿色
                        logging.debug(f"更新UI: 行索引={list_idx}, 幻灯片={slide_index}")
            except Exception as e:
                logging.error(f"更新幻灯片列表项时出错: {str(e)}")
        
        # 如果当前有选中的幻灯片，更新UI状态
        current_row = self.slide_list.currentRow()
        if current_row >= 0 and current_row < len(self.all_slides):
            current_slide_index = self.all_slides[current_row]
            logging.debug(f"更新当前选中幻灯片UI: 行索引={current_row}, 幻灯片={current_slide_index}")
            
            self.update_decision_buttons(current_slide_index)
            # 更新当前显示的修改项UI
            self.update_text_comparison(current_slide_index)
    
    def select_none_slides(self):
        """不选择任何幻灯片（全部拒绝修改）"""
        logging.info("设置所有幻灯片为拒绝状态")
        
        if not hasattr(self, 'user_choices'):
            self.user_choices = {}
            
        # 遍历所有有修改的幻灯片，设置为拒绝
        for slide_index in self.slides_with_changes:
            # 记录用户选择
            self.user_choices[slide_index] = False
            logging.debug(f"设置幻灯片 {slide_index} 为拒绝状态")
            
            # 更新幻灯片的所有修改项决策
            if slide_index in self.text_changes:
                changes = self.text_changes[slide_index].get("changes", [])
                
                # 确保决策字典存在
                if slide_index not in self.change_decisions:
                    self.change_decisions[slide_index] = {}
                    
                # 更新所有修改项的决策
                for j in range(len(changes)):
                    change_id = f"{slide_index}_{j}"
                    self.change_decisions[slide_index][change_id] = False
                    logging.debug(f"设置修改项 {change_id} 为拒绝状态")
            
            # 更新列表项显示 - 通过all_slides查找行索引
            try:
                list_idx = self.all_slides.index(slide_index) if slide_index in self.all_slides else -1
                if list_idx >= 0 and list_idx < self.slide_list.count():
                    item = self.slide_list.item(list_idx)
                    if item:
                        item.setText(f"幻灯片 {slide_index} ✗")
                        item.setForeground(QColor("#F44336"))  # 红色
                        logging.debug(f"更新UI: 行索引={list_idx}, 幻灯片={slide_index}")
            except Exception as e:
                logging.error(f"更新幻灯片列表项时出错: {str(e)}")
        
        # 如果当前有选中的幻灯片，更新UI状态
        current_row = self.slide_list.currentRow()
        if current_row >= 0 and current_row < len(self.all_slides):
            current_slide_index = self.all_slides[current_row]
            logging.debug(f"更新当前选中幻灯片UI: 行索引={current_row}, 幻灯片={current_slide_index}")
            
            self.update_decision_buttons(current_slide_index)
            # 更新当前显示的修改项UI
            self.update_text_comparison(current_slide_index)
    
    def apply_changes(self):
        """应用用户选择的修改"""
        try:
            # 显示说明对话框
            explanation = """
您将应用选择的修改。系统会重新处理原始PPT，
只对您选择接受的文本部分进行修改，保留您选择拒绝的部分为原始文本。

此过程可能需要几分钟时间，具体取决于PPT文件大小和修改数量。
是否继续？
            """
            
            reply = QMessageBox.question(
                self, "应用选择的修改", explanation,
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply != QMessageBox.No:
                # 显示进度对话框
                progress_dialog = QMessageBox()
                progress_dialog.setWindowTitle("处理中")
                progress_dialog.setText("正在精确应用选择的修改，请稍候...")
                progress_dialog.setStandardButtons(QMessageBox.NoButton)
                progress_dialog.show()
                QApplication.processEvents()
                
                # 创建API密钥管理器
                try:
                    from ..api.api_manager import APIKeyManager
                    from ..processor.text_processor import TextProcessor
                    import copy
                    
                    # 使用存储的文本修改记录，创建PPT处理器
                    ppt_processor = PPTProcessor(
                        self.api_key_manager if hasattr(self, 'api_key_manager') else APIKeyManager(os.environ.get('API_KEYS_FILE', '')),
                        self.text_processor if hasattr(self, 'text_processor') else TextProcessor(
                            APIKeyManager(os.environ.get('API_KEYS_FILE', '')),
                            "请帮我润色以下文本",
                            15
                        )
                    )
                    
                    # 使用深拷贝确保完全复制修改记录，包括嵌套字典和列表
                    ppt_processor.text_changes = copy.deepcopy(self.text_changes)
                    
                    # 添加详细日志记录
                    for slide_idx, slide_changes in self.text_changes.items():
                        if "changes" in slide_changes:
                            for i, change in enumerate(slide_changes["changes"]):
                                if "modified" in change:
                                    logging.info(f"应用前: text_changes[{slide_idx}]['changes'][{i}]['modified'] = '{change['modified']}'")
                    
                    # 创建输出路径
                    final_path = os.path.splitext(self.modified_ppt_path)[0] + "_selective.pptx"
                    
                    # 应用选择性修改
                    logging.info("开始应用选择性修改...")
                    result_path = ppt_processor.apply_selective_changes(
                        self.original_ppt_path,
                        final_path,
                        self.change_decisions,
                        self.slides_to_modify
                    )
                    
                    # 关闭进度对话框
                    progress_dialog.close()
                    
                    # 发出完成信号
                    self.preview_completed.emit([result_path])
                    
                    # 获取失败页面信息 - 直接从ppt_processor的failed_pages属性获取
                    failed_pages = []
                    if hasattr(ppt_processor, 'failed_pages') and ppt_processor.failed_pages:
                        failed_pages = sorted(ppt_processor.failed_pages)
                    
                    # 显示成功消息
                    success_msg = f"""
选择性修改已成功应用！

新文件已保存为: {os.path.basename(result_path)}
"""

                    # 无论是否有失败页面，都显示失败页面信息
                    if failed_pages:
                        failed_pages_str = ", ".join([str(page) for page in failed_pages])
                        success_msg += f"\n应用失败的幻灯片: {failed_pages_str}"
                    else:
                        success_msg += "\n失败页面: 无"
                    
                    success_msg += "\n\n是否现在打开此文件？"
                    
                    reply = QMessageBox.question(
                        self, "应用成功", success_msg,
                        QMessageBox.Yes | QMessageBox.No
                    )
                    
                    if reply == QMessageBox.Yes:
                        # 打开文件
                        import subprocess
                        import platform
                        from PyQt5.QtCore import QTimer

                        # 先打开原始文件
                        if os.path.exists(self.original_ppt_path):
                            if platform.system() == 'Windows':
                                os.startfile(self.original_ppt_path)
                            elif platform.system() == 'Darwin':  # macOS
                                subprocess.call(('open', self.original_ppt_path))
                            else:  # Linux
                                subprocess.call(('xdg-open', self.original_ppt_path))
                        
                        # 使用QTimer延时0.5秒后再打开selective文件
                        final_result_path = result_path  # 保存路径，以便在Timer回调函数中使用
                        
                        def open_selective_file():
                            """延时0.5秒后打开selective文件"""
                            if platform.system() == 'Windows':
                                os.startfile(final_result_path)
                            elif platform.system() == 'Darwin':  # macOS
                                subprocess.call(('open', final_result_path))
                            else:  # Linux
                                subprocess.call(('xdg-open', final_result_path))
                                
                        # 创建并启动定时器    
                        QTimer.singleShot(500, open_selective_file)  # 500毫秒 = 0.5秒
                    
                    # 关闭窗口
                    self.close()
                    
                except ImportError as e:
                    logging.error(f"导入所需模块失败: {str(e)}")
                    QMessageBox.critical(self, "错误", f"缺少必要的模块: {str(e)}")
                    progress_dialog.close()
                except Exception as e:
                    logging.error(f"应用选择性修改时发生错误: {str(e)}")
                    QMessageBox.critical(self, "错误", f"应用选择性修改时发生错误: {str(e)}")
                    progress_dialog.close()
        except Exception as e:
            logging.error(f"应用修改时发生错误: {str(e)}")
            QMessageBox.warning(self, "应用失败", f"应用修改时发生错误: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 停止渲染线程
            if hasattr(self, 'original_renderer') and self.original_renderer is not None:
                self.original_renderer.request_stop()
            
            if hasattr(self, 'modified_renderer') and self.modified_renderer is not None:
                self.modified_renderer.request_stop()
            
            # 等待渲染线程结束
            timeout_ms = 2000  # 最多等待2秒
            
            if hasattr(self, 'original_renderer') and self.original_renderer is not None:
                self.original_renderer.wait(timeout_ms)
            
            if hasattr(self, 'modified_renderer') and self.modified_renderer is not None:
                self.modified_renderer.wait(timeout_ms)
                
            # 清理临时目录
            if hasattr(self, 'temp_dir') and self.temp_dir and os.path.exists(self.temp_dir):
                try:
                    shutil.rmtree(self.temp_dir)
                    logging.debug(f"已删除临时目录: {self.temp_dir}")
                except Exception as e:
                    logging.warning(f"清理临时目录失败: {str(e)}")
                    
        except Exception as e:
            logging.warning(f"关闭窗口时发生错误: {str(e)}")
            
        # 接受关闭事件
        event.accept()

    def handle_change_decision(self, change_item, is_accepted):
        """处理单个修改项的决策变更"""
        # 获取当前幻灯片索引
        current_row = self.slide_list.currentRow()
        if current_row < 0 or current_row >= len(self.all_slides):
            logging.warning(f"无效的幻灯片行索引: {current_row}")
            return
            
        # 使用all_slides获取真实的幻灯片索引
        slide_index = self.all_slides[current_row]
        logging.info(f"处理修改项决策变更: 行索引={current_row}, 幻灯片索引={slide_index}, 接受={is_accepted}")
        
        # 确保change_item有change_id属性
        if not hasattr(change_item, 'change_id'):
            logging.error("修改项缺少change_id属性")
            return
            
        # 确保幻灯片的决策字典存在
        if slide_index not in self.change_decisions:
            self.change_decisions[slide_index] = {}
            
        # 记录决策
        self.change_decisions[slide_index][change_item.change_id] = is_accepted
        logging.debug(f"设置修改项决策: {change_item.change_id} = {is_accepted}")
        
        # 检查当前幻灯片的所有修改是否都有相同的决策
        all_same = True
        current_decisions = self.change_decisions[slide_index]
        first_decision = None
        
        for decision in current_decisions.values():
            if first_decision is None:
                first_decision = decision
            elif decision != first_decision:
                all_same = False
                break
        
        # 如果所有修改项都有相同的决策，更新幻灯片的整体决策
        if all_same and first_decision is not None:
            self.user_choices[slide_index] = first_decision
            logging.debug(f"所有修改项有相同决策，更新幻灯片整体决策: {slide_index} = {first_decision}")
            
            # 更新决策按钮状态
            self.update_decision_buttons(slide_index)
            
            # 更新列表项的显示
            try:
                current_item = self.slide_list.currentItem()
                if current_item:
                    if first_decision:
                        current_item.setText(f"幻灯片 {slide_index} ✓")
                        current_item.setForeground(QColor("#4CAF50"))
                    else:
                        current_item.setText(f"幻灯片 {slide_index} ✗")
                        current_item.setForeground(QColor("#F44336"))
                    logging.debug(f"更新幻灯片列表项: {slide_index} 为 {'接受' if first_decision else '拒绝'}")
            except Exception as e:
                logging.error(f"更新列表项显示时出错: {str(e)}")
        else:
            # 混合决策，显示为部分接受
            self.user_choices[slide_index] = None  # 使用None表示混合状态
            logging.debug(f"修改项有不同决策，设置幻灯片为混合状态: {slide_index}")
            
            # 更新列表项显示
            try:
                current_item = self.slide_list.currentItem()
                if current_item:
                    current_item.setText(f"幻灯片 {slide_index} ⊞")  # 使用特殊标记表示部分接受
                    current_item.setForeground(QColor("#FFC107"))  # 使用黄色表示部分接受
                    logging.debug(f"更新幻灯片列表项为混合状态: {slide_index}")
            except Exception as e:
                logging.error(f"更新列表项为混合状态时出错: {str(e)}")
    
    def show_prev_slide(self):
        """显示上一张幻灯片"""
        if self.current_slide > 0:
            self.current_slide -= 1
            # 更新列表选中项
            self.slide_list.setCurrentRow(self.current_slide)
            # 显示当前幻灯片
            self.show_current_slide()
    
    def show_next_slide(self):
        """显示下一张幻灯片"""
        if hasattr(self, 'all_slides') and self.all_slides and self.current_slide < len(self.all_slides) - 1:
            self.current_slide += 1
            # 更新列表选中项
            self.slide_list.setCurrentRow(self.current_slide)
            # 显示当前幻灯片
            self.show_current_slide()
    
    def show_current_slide(self):
        """显示当前幻灯片"""
        if not hasattr(self, 'original_slides') or not hasattr(self, 'modified_slides'):
            logging.warning("幻灯片列表未初始化")
            return
            
        if not self.original_slides or not self.modified_slides:
            logging.warning("幻灯片图像未加载")
            return
        
        current_row = self.slide_list.currentRow()
        if current_row < 0 or current_row >= len(self.all_slides):
            logging.warning(f"当前行索引无效: {current_row}, 有效范围: 0-{len(self.all_slides)-1}")
            return
            
        # 获取当前幻灯片索引 - 使用all_slides而非slides_to_modify
        slide_index = self.all_slides[current_row]
        self.current_slide_index = slide_index
        # 更新当前幻灯片行索引，用于翻页和状态栏显示
        self.current_slide = current_row
        logging.debug(f"显示当前幻灯片: 行索引={current_row}, 幻灯片索引={slide_index}")
        
        # 确保索引有效
        if slide_index <= 0 or slide_index > len(self.original_slides):
            logging.warning(f"幻灯片索引无效: {slide_index}, 有效范围: 1-{len(self.original_slides)}")
            return
            
        try:
            # 获取对应的图像路径
            original_image = self.original_slides[slide_index - 1]
            modified_image = self.modified_slides[slide_index - 1]
            
            # 保存当前缩放因子
            original_zoom = 1.0
            modified_zoom = 1.0
            if hasattr(self, 'original_viewer') and hasattr(self.original_viewer, 'zoom_factor'):
                original_zoom = self.original_viewer.zoom_factor
            if hasattr(self, 'modified_viewer') and hasattr(self.modified_viewer, 'zoom_factor'):
                modified_zoom = self.modified_viewer.zoom_factor
            
            # 显示原始幻灯片
            if original_image:
                self.original_viewer.display_slide(original_image, is_original=True)
                # 确保保持相同的缩放级别
                if original_zoom != 1.0:
                    self.original_viewer.setTransform(QTransform().scale(original_zoom, original_zoom))
                    self.original_viewer.zoom_factor = original_zoom
            else:
                self.original_viewer.show_message(f"幻灯片 {slide_index} 原始图像未加载")
            
            # 显示修改后的幻灯片
            if modified_image:
                self.modified_viewer.display_slide(modified_image, is_original=False)
                # 确保保持相同的缩放级别
                if modified_zoom != 1.0:
                    self.modified_viewer.setTransform(QTransform().scale(modified_zoom, modified_zoom))
                    self.modified_viewer.zoom_factor = modified_zoom
            else:
                self.modified_viewer.show_message(f"幻灯片 {slide_index} 修改后图像未加载")
            
            # 更新文本对比区域 - 仅当幻灯片有修改时显示
            if slide_index in self.slides_with_changes:
                self.update_text_comparison(slide_index)
            else:
                # 清空文本对比区域，显示无修改信息
                if hasattr(self, 'changes_layout'):
                    # 清空布局中的所有控件
                    while self.changes_layout.count() > 0:
                        item = self.changes_layout.takeAt(0)
                        if item.widget():
                            item.widget().deleteLater()
                    
                    # 添加无修改提示
                    no_changes_label = QLabel("此幻灯片没有文本修改")
                    no_changes_label.setAlignment(Qt.AlignCenter)
                    no_changes_label.setStyleSheet("color: gray; font-style: italic;")
                    self.changes_layout.addWidget(no_changes_label)
                    
                    # 添加伸缩空间
                    self.changes_layout.addStretch()
            
            # 更新进度指示器
            self.update_progress_indicator()
            
            # 更新导航按钮状态
            self.update_navigation_buttons()
            
            # 更新决策按钮状态 - 仅当幻灯片有修改时启用
            if hasattr(self, 'accept_button') and hasattr(self, 'reject_button'):
                has_changes = slide_index in self.slides_with_changes
                self.accept_button.setEnabled(has_changes)
                self.reject_button.setEnabled(has_changes)
                
            if hasattr(self, 'update_decision_buttons'):
                self.update_decision_buttons(slide_index)
        except Exception as e:
            logging.error(f"显示幻灯片 {slide_index} 时出错: {str(e)}")
            self.original_viewer.show_message(f"显示幻灯片出错: {str(e)}")
            self.modified_viewer.show_message(f"显示幻灯片出错: {str(e)}")
    
    def update_progress_indicator(self):
        """更新进度指示器"""
        # 确保current_slide是有效索引
        if not hasattr(self, 'all_slides') or not self.all_slides:
            return
            
        # 使用all_slides的长度作为总数，更新状态栏
        total_slides = len(self.all_slides)
        current_slide_number = self.current_slide + 1  # 显示给用户时从1开始计数
        
        # 更新状态栏显示
        self.statusBar().showMessage(f"幻灯片: {current_slide_number}/{total_slides}")
        
        # 如果需要在header_widget可见的情况下更新progress_label（主要为兼容其他代码），保留这部分逻辑
        if hasattr(self, 'progress_label') and hasattr(self, 'header_widget') and self.header_widget.isVisible():
            self.progress_label.setText(f"{current_slide_number}/{total_slides}")
        
        # 调试日志
        logging.debug(f"更新进度指示器: {current_slide_number}/{total_slides}")
    
    def update_navigation_buttons(self):
        """更新导航按钮状态 - 现在只记录日志，因为按钮已被移除"""
        if hasattr(self, 'all_slides') and self.all_slides:
            # 确保all_slides存在且非空
            total_slides = len(self.all_slides)
            # 仅记录导航状态
            logging.debug(f"导航状态: 可向前={self.current_slide > 0}, 可向后={self.current_slide < total_slides - 1}")
    
    def highlight_text_change(self, region_id):
        """
        高亮显示文本修改
        
        Args:
            region_id: 区域ID
        """
        # 获取当前幻灯片的修改信息
        if self.current_slide in self.text_changes:
            slide_changes = self.text_changes[self.current_slide]
            
            # 确定格式
            changes_list = []
            if isinstance(slide_changes, list):
                changes_list = slide_changes
            elif isinstance(slide_changes, dict) and "changes" in slide_changes:
                changes_list = slide_changes["changes"]
            
            # 找到点击的修改项
            idx = int(region_id)
            if 0 <= idx < len(changes_list):
                change = changes_list[idx]
                
                # 显示修改详情
                original_text = change.get("original_text", "")
                modified_text = change.get("modified_text", "")
                
                # 在状态栏显示修改信息
                message = f"修改 #{idx+1}: '{original_text}' → '{modified_text}'"
                self.status_bar.showMessage(message, 5000)  # 显示5秒
                
                # 可以在这里添加更多的高亮效果，比如在文本区域显示详细的修改信息
                logger.debug(f"高亮显示文本修改: {idx+1}")
        else:
            self.status_bar.showMessage("当前幻灯片没有文本修改", 3000)
    
    # 添加水平滚动文本修改区域的方法
    def scroll_text_left(self):
        """向左滚动文本修改区域"""
        scrollbar = self.text_scroll_area.horizontalScrollBar()
        scrollbar.setValue(scrollbar.value() - 300)  # 每次滚动一个修改项的宽度
        
    def scroll_text_right(self):
        """向右滚动文本修改区域"""
        scrollbar = self.text_scroll_area.horizontalScrollBar()
        scrollbar.setValue(scrollbar.value() + 300)  # 每次滚动一个修改项的宽度
    
    def save_manual_edit(self, text_edit, slide_index, change_id):
        """保存用户手动编辑的修改后文本
        
        Args:
            text_edit: 文本编辑器控件
            slide_index: 幻灯片索引
            change_id: 修改项ID
        """
        logging.info(f"保存手动修改: 幻灯片={slide_index}, 修改项={change_id}")
        
        try:
            # 获取用户编辑的新文本
            new_text = text_edit.toPlainText().strip()
            
            # 提取修改项序号
            change_idx = int(change_id.split('_')[1])
            
            # 验证text_changes结构
            if slide_index not in self.text_changes:
                logging.error(f"幻灯片 {slide_index} 在text_changes中不存在")
                QMessageBox.warning(self, "保存失败", f"幻灯片 {slide_index} 的修改记录不存在。")
                return
                
            if "changes" not in self.text_changes[slide_index]:
                logging.error(f"幻灯片 {slide_index} 的text_changes结构不正确")
                QMessageBox.warning(self, "保存失败", f"幻灯片 {slide_index} 的修改记录结构不正确。")
                return
                
            changes = self.text_changes[slide_index]["changes"]
            if change_idx >= len(changes):
                logging.error(f"修改项索引 {change_idx} 超出范围")
                QMessageBox.warning(self, "保存失败", f"修改项 {change_id} 的索引超出范围。")
                return
            
            # 获取原始的修改项
            change = changes[change_idx]
            
            # 保存原始修改后文本用于比较
            original_modified_text = ""
            if "modified" in change:
                original_modified_text = change["modified"]
                logging.info(f"原始字段'modified'值: '{original_modified_text}'")
            elif "modified_text" in change:
                original_modified_text = change["modified_text"]
                logging.info(f"原始字段'modified_text'值: '{original_modified_text}'")
            
            # 检查文本是否有变化
            if new_text == original_modified_text:
                QMessageBox.information(self, "无需保存", "文本没有变化，无需保存。")
                return
            
            # 保存AI修改的版本，用于后续在preview文件中查找
            if "ai_modified" not in change:
                # 第一次手动修改，保存AI的修改版本
                if "modified" in change:
                    change["ai_modified"] = original_modified_text
                    logging.info(f"保存AI修改版本到'ai_modified': '{original_modified_text}'")
                elif "modified_text" in change:
                    change["ai_modified"] = original_modified_text
                    logging.info(f"保存AI修改版本到'ai_modified': '{original_modified_text}'")
            
            # 更新修改后的文本
            if "modified" in change:
                change["modified"] = new_text
                logging.info(f"更新字段'modified'为: '{new_text}'")
            elif "modified_text" in change:
                change["modified_text"] = new_text
                logging.info(f"更新字段'modified_text'为: '{new_text}'")
            else:
                change["modified"] = new_text
                logging.info(f"新增字段'modified'为: '{new_text}'")
            
            # 标记为手动修改
            change["manually_edited"] = True
            
            # 自动设置为接受
            if slide_index not in self.change_decisions:
                self.change_decisions[slide_index] = {}
            self.change_decisions[slide_index][change_id] = True
            
            # 添加详细日志以便调试
            logging.info(f"修改后的text_changes[{slide_index}]['changes'][{change_idx}]: {change}")
            
            # 更新显示
            self.update_text_comparison(slide_index)
            
            # 显示成功消息
            QMessageBox.information(self, "保存成功", f"已保存手动修改的文本。\n\n原文本：{original_modified_text}\n\n新文本：{new_text}")
            
            logging.info(f"成功保存手动修改: 幻灯片={slide_index}, 修改项={change_id}")
            
        except Exception as e:
            logging.error(f"保存手动修改时发生错误: {str(e)}")
            QMessageBox.warning(self, "保存失败", f"保存手动修改时发生错误: {str(e)}")
    
    def wheelEvent(self, event):
        """处理鼠标滚轮事件，实现幻灯片翻页和缩放"""
        # 获取滚动方向
        delta = event.angleDelta().y()
        
        # 获取键盘修饰键状态
        modifiers = event.modifiers()
        ctrl_pressed = bool(modifiers & Qt.ControlModifier)
        
        # 获取鼠标位置
        pos = event.pos()
        
        # 检查鼠标是否在SlideViewer组件上
        is_on_original_viewer = False
        is_on_modified_viewer = False
        
        # 检查原始幻灯片查看器
        if hasattr(self, 'original_viewer'):
            original_global_rect = self.original_viewer.mapToGlobal(self.original_viewer.rect().topLeft())
            original_local_rect = QRect(
                self.mapFromGlobal(original_global_rect),
                self.original_viewer.size()
            )
            if original_local_rect.contains(pos):
                is_on_original_viewer = True
        
        # 检查修改后幻灯片查看器
        if hasattr(self, 'modified_viewer'):
            modified_global_rect = self.modified_viewer.mapToGlobal(self.modified_viewer.rect().topLeft())
            modified_local_rect = QRect(
                self.mapFromGlobal(modified_global_rect),
                self.modified_viewer.size()
            )
            if modified_local_rect.contains(pos):
                is_on_modified_viewer = True
        
        # 仅当按住Ctrl键且鼠标在幻灯片查看器上时，才处理缩放
        if ctrl_pressed and (is_on_original_viewer or is_on_modified_viewer):
            # 控制缩放
            if delta > 0:
                # 放大
                factor = 1.1
            else:
                # 缩小
                factor = 0.9
            
            # 应用缩放到对应的查看器
            if is_on_original_viewer and hasattr(self, 'original_viewer'):
                current_zoom = self.original_viewer.zoom_factor
                new_zoom = current_zoom * factor
                new_zoom = max(0.1, min(new_zoom, 10.0))  # 限制缩放范围
                self.original_viewer.zoom_factor = new_zoom
                self.original_viewer.setTransform(QTransform().scale(new_zoom, new_zoom))
                logging.debug(f"原始幻灯片查看器缩放到 {new_zoom:.2f}")
            
            if is_on_modified_viewer and hasattr(self, 'modified_viewer'):
                current_zoom = self.modified_viewer.zoom_factor
                new_zoom = current_zoom * factor
                new_zoom = max(0.1, min(new_zoom, 10.0))  # 限制缩放范围
                self.modified_viewer.zoom_factor = new_zoom
                self.modified_viewer.setTransform(QTransform().scale(new_zoom, new_zoom))
                logging.debug(f"修改后幻灯片查看器缩放到 {new_zoom:.2f}")
            
            # 在状态栏显示当前缩放比例
            if is_on_original_viewer:
                self.statusBar().showMessage(f"原始幻灯片缩放: {self.original_viewer.zoom_factor:.0%}", 2000)
            else:
                self.statusBar().showMessage(f"修改后幻灯片缩放: {self.modified_viewer.zoom_factor:.0%}", 2000)
            
            # 标记事件已处理
            event.accept()
            return
        
        # 如果不是缩放操作，则处理翻页功能
        # 根据滚动方向翻页
        if delta > 0:  # 向上滚动，显示上一张
            self.show_prev_slide()
        elif delta < 0:  # 向下滚动，显示下一张
            self.show_next_slide()
            
        # 标记事件已处理
        event.accept()


class SlideListWidget(QListWidget):
    """自定义幻灯片列表控件，支持鼠标滚轮翻页功能"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
    
    def wheelEvent(self, event):
        """处理鼠标滚轮事件，实现幻灯片翻页"""
        # 获取当前选中的行
        current_row = self.currentRow()
        total_rows = self.count()
        
        # 确定滚动方向
        delta = event.angleDelta().y()
        
        new_row = current_row
        if delta > 0:  # 向上滚动，显示上一张
            if current_row > 0:
                new_row = current_row - 1
        elif delta < 0:  # 向下滚动，显示下一张
            if current_row < total_rows - 1:
                new_row = current_row + 1
        
        # 仅当行真正改变时才进行更新
        if new_row != current_row:
            # 设置新的当前行
            self.setCurrentRow(new_row)
            
            # 直接调用父窗口的方法来确保幻灯片更新
            if hasattr(self.parent_window, 'show_current_slide'):
                self.parent_window.show_current_slide()
                
            # 直接更新状态栏显示
            if hasattr(self.parent_window, 'update_progress_indicator'):
                self.parent_window.update_progress_indicator()
        
        # 事件已处理
        event.accept()